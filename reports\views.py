from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Count, Sum
from .models import ReportTemplate, GeneratedReport, Notification, SystemAlert

class ReportsDashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/reports_dashboard.html'

class InventoryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/inventory_report.html'

class LowStockReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/low_stock_report.html'

class ExpiryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/expiry_report.html'

class InventoryMovementReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/inventory_movement_report.html'

class OrderReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/order_report.html'

class PendingOrdersReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/pending_orders_report.html'

class FulfilledOrdersReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/fulfilled_orders_report.html'

class OrdersByInstitutionReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/orders_by_institution_report.html'

class UsageReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/usage_report.html'

class UsageByDrugReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/usage_by_drug_report.html'

class UsageByDepartmentReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/usage_by_department_report.html'

class UsageTrendsReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/usage_trends_report.html'

class FinancialReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/financial_report.html'

class CostAnalysisReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/cost_analysis_report.html'

class SupplierSpendingReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/supplier_spending_report.html'

class SupplierReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/supplier_report.html'

class SupplierPerformanceReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/supplier_performance_report.html'

class SupplierDeliveryReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/supplier_delivery_report.html'

class CustomReportView(LoginRequiredMixin, TemplateView):
    template_name = 'reports/custom_report.html'

class ReportTemplateListView(LoginRequiredMixin, ListView):
    model = ReportTemplate
    template_name = 'reports/report_template_list.html'
    context_object_name = 'templates'
    paginate_by = 20

class ReportTemplateDetailView(LoginRequiredMixin, DetailView):
    model = ReportTemplate
    template_name = 'reports/report_template_detail.html'
    context_object_name = 'template'

class ReportTemplateCreateView(LoginRequiredMixin, CreateView):
    model = ReportTemplate
    template_name = 'reports/report_template_form.html'
    fields = ['name', 'report_type', 'description', 'is_public']
    success_url = reverse_lazy('reports:report_template_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        return super().form_valid(form)

class ReportTemplateUpdateView(LoginRequiredMixin, UpdateView):
    model = ReportTemplate
    template_name = 'reports/report_template_form.html'
    fields = ['name', 'report_type', 'description', 'is_public']
    success_url = reverse_lazy('reports:report_template_list')

class ReportTemplateDeleteView(LoginRequiredMixin, DeleteView):
    model = ReportTemplate
    template_name = 'reports/report_template_confirm_delete.html'
    success_url = reverse_lazy('reports:report_template_list')

class GeneratedReportListView(LoginRequiredMixin, ListView):
    model = GeneratedReport
    template_name = 'reports/generated_report_list.html'
    context_object_name = 'reports'
    paginate_by = 20

class GeneratedReportDetailView(LoginRequiredMixin, DetailView):
    model = GeneratedReport
    template_name = 'reports/generated_report_detail.html'
    context_object_name = 'report'

class GeneratedReportDeleteView(LoginRequiredMixin, DeleteView):
    model = GeneratedReport
    template_name = 'reports/generated_report_confirm_delete.html'
    success_url = reverse_lazy('reports:generated_report_list')

# Action Views
@login_required
def generate_report_from_template(request, pk):
    template = get_object_or_404(ReportTemplate, pk=pk)
    # Implementation for generating report from template
    messages.success(request, f'تم توليد التقرير من القالب {template.name}')
    return redirect('reports:report_template_detail', pk=pk)

@login_required
def download_generated_report(request, pk):
    report = get_object_or_404(GeneratedReport, pk=pk)
    # Implementation for downloading generated report
    return HttpResponse(f"تحميل التقرير {report.title}")

# Export Views
@login_required
def export_to_excel(request):
    # Implementation for Excel export
    return HttpResponse("تصدير إلى Excel")

@login_required
def export_to_pdf(request):
    # Implementation for PDF export
    return HttpResponse("تصدير إلى PDF")

@login_required
def export_to_csv(request):
    # Implementation for CSV export
    return HttpResponse("تصدير إلى CSV")

# API Views
def chart_data_api(request):
    """API لبيانات الرسوم البيانية"""
    chart_type = request.GET.get('type', 'orders')

    if chart_type == 'orders':
        # Sample data for orders chart
        data = {
            'labels': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            'datasets': [{
                'label': 'الطلبات',
                'data': [12, 19, 3, 5, 2, 3],
                'borderColor': 'rgb(75, 192, 192)',
                'tension': 0.1
            }]
        }
    else:
        data = {'labels': [], 'datasets': []}

    return JsonResponse(data)

def report_preview_api(request):
    """API لمعاينة التقرير"""
    template_id = request.GET.get('template_id')
    if template_id:
        template = get_object_or_404(ReportTemplate, pk=template_id)
        # Implementation for report preview
        return JsonResponse({'status': 'success', 'preview': 'معاينة التقرير'})

    return JsonResponse({'status': 'error', 'message': 'Template ID required'})
