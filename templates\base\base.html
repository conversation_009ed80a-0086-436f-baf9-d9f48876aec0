<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الأدوية{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    {% load static %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard' %}">
                <i class="fas fa-pills me-2"></i>
                نظام إدارة الأدوية
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard' %}">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    
                    {% if user.is_authenticated %}
                    <!-- Drugs Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-pills"></i> الأدوية
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'drugs:drug_list' %}">قائمة الأدوية</a></li>
                            <li><a class="dropdown-item" href="{% url 'drugs:drug_create' %}">إضافة دواء</a></li>
                            <li><a class="dropdown-item" href="{% url 'drugs:category_list' %}">التصنيفات</a></li>
                            <li><a class="dropdown-item" href="{% url 'drugs:manufacturer_list' %}">الشركات المصنعة</a></li>
                        </ul>
                    </li>
                    
                    <!-- Inventory Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-warehouse"></i> المخزون
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'inventory:inventory_list' %}">حالة المخزون</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:warehouse_list' %}">المستودعات</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:movement_list' %}">حركات المخزون</a></li>
                            <li><a class="dropdown-item" href="{% url 'inventory:low_stock' %}">المخزون المنخفض</a></li>
                        </ul>
                    </li>
                    
                    <!-- Orders Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-shopping-cart"></i> الطلبات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'orders:order_list' %}">طلبات الصرف</a></li>
                            <li><a class="dropdown-item" href="{% url 'orders:internal_request_list' %}">الطلبات الداخلية</a></li>
                            <li><a class="dropdown-item" href="{% url 'orders:order_create' %}">طلب جديد</a></li>
                        </ul>
                    </li>
                    
                    <!-- Procurement Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i> المشتريات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'procurement:purchase_order_list' %}">أوامر الشراء</a></li>
                            <li><a class="dropdown-item" href="{% url 'procurement:supplier_list' %}">الموردين</a></li>
                            <li><a class="dropdown-item" href="{% url 'procurement:purchase_order_create' %}">أمر شراء جديد</a></li>
                        </ul>
                    </li>
                    
                    <!-- Reports Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'reports:inventory_report' %}">تقرير المخزون</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:expiry_report' %}">تقرير انتهاء الصلاحية</a></li>
                            <li><a class="dropdown-item" href="{% url 'reports:usage_report' %}">تقرير الاستخدام</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:notifications' %}">التنبيهات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:logout' %}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">تسجيل الدخول</a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid mt-4">
        <!-- Alerts -->
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Breadcrumb -->
        {% block breadcrumb %}{% endblock %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 نظام إدارة الأدوية. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
