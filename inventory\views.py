from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Sum, F
from .models import Warehouse, InventoryItem, InventoryMovement, DrugBatch

class InventoryListView(LoginRequiredMixin, ListView):
    model = InventoryItem
    template_name = 'inventory/inventory_list.html'
    context_object_name = 'inventory_items'
    paginate_by = 20

    def get_queryset(self):
        queryset = InventoryItem.objects.select_related('drug', 'warehouse', 'batch')

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(drug__commercial_name__icontains=search) |
                Q(drug__scientific_name__icontains=search)
            )

        # Warehouse filter
        warehouse = self.request.GET.get('warehouse')
        if warehouse:
            queryset = queryset.filter(warehouse_id=warehouse)

        return queryset.order_by('drug__commercial_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['warehouses'] = Warehouse.objects.filter(is_active=True)
        return context

class InventoryItemDetailView(LoginRequiredMixin, DetailView):
    model = InventoryItem
    template_name = 'inventory/inventory_item_detail.html'
    context_object_name = 'item'

class InventoryItemUpdateView(LoginRequiredMixin, UpdateView):
    model = InventoryItem
    template_name = 'inventory/inventory_item_form.html'
    fields = ['minimum_stock', 'maximum_stock', 'shelf_location']
    success_url = reverse_lazy('inventory:inventory_list')

class WarehouseListView(LoginRequiredMixin, ListView):
    model = Warehouse
    template_name = 'inventory/warehouse_list.html'
    context_object_name = 'warehouses'
    paginate_by = 20

class WarehouseDetailView(LoginRequiredMixin, DetailView):
    model = Warehouse
    template_name = 'inventory/warehouse_detail.html'
    context_object_name = 'warehouse'

class WarehouseCreateView(LoginRequiredMixin, CreateView):
    model = Warehouse
    template_name = 'inventory/warehouse_form.html'
    fields = ['name', 'code', 'location', 'manager', 'phone', 'capacity', 'is_main_warehouse']
    success_url = reverse_lazy('inventory:warehouse_list')

class WarehouseUpdateView(LoginRequiredMixin, UpdateView):
    model = Warehouse
    template_name = 'inventory/warehouse_form.html'
    fields = ['name', 'code', 'location', 'manager', 'phone', 'capacity', 'is_main_warehouse']
    success_url = reverse_lazy('inventory:warehouse_list')

class WarehouseDeleteView(LoginRequiredMixin, DeleteView):
    model = Warehouse
    template_name = 'inventory/warehouse_confirm_delete.html'
    success_url = reverse_lazy('inventory:warehouse_list')

class BatchListView(LoginRequiredMixin, ListView):
    model = DrugBatch
    template_name = 'inventory/batch_list.html'
    context_object_name = 'batches'
    paginate_by = 20

class BatchDetailView(LoginRequiredMixin, DetailView):
    model = DrugBatch
    template_name = 'inventory/batch_detail.html'
    context_object_name = 'batch'

class BatchCreateView(LoginRequiredMixin, CreateView):
    model = DrugBatch
    template_name = 'inventory/batch_form.html'
    fields = ['drug', 'batch_number', 'manufacturing_date', 'expiry_date', 'supplier_batch_number', 'cost_price']
    success_url = reverse_lazy('inventory:batch_list')

class BatchUpdateView(LoginRequiredMixin, UpdateView):
    model = DrugBatch
    template_name = 'inventory/batch_form.html'
    fields = ['drug', 'batch_number', 'manufacturing_date', 'expiry_date', 'supplier_batch_number', 'cost_price']
    success_url = reverse_lazy('inventory:batch_list')

class BatchDeleteView(LoginRequiredMixin, DeleteView):
    model = DrugBatch
    template_name = 'inventory/batch_confirm_delete.html'
    success_url = reverse_lazy('inventory:batch_list')

class MovementListView(LoginRequiredMixin, ListView):
    model = InventoryMovement
    template_name = 'inventory/movement_list.html'
    context_object_name = 'movements'
    paginate_by = 20

class MovementDetailView(LoginRequiredMixin, DetailView):
    model = InventoryMovement
    template_name = 'inventory/movement_detail.html'
    context_object_name = 'movement'

class MovementCreateView(LoginRequiredMixin, CreateView):
    model = InventoryMovement
    template_name = 'inventory/movement_form.html'
    fields = ['inventory_item', 'movement_type', 'quantity', 'reference_number', 'notes']
    success_url = reverse_lazy('inventory:movement_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        return super().form_valid(form)

class LowStockView(LoginRequiredMixin, ListView):
    model = InventoryItem
    template_name = 'inventory/low_stock.html'
    context_object_name = 'low_stock_items'

    def get_queryset(self):
        return InventoryItem.objects.select_related('drug', 'warehouse').filter(
            quantity__lte=F('minimum_stock')
        )

class ExpiredDrugsView(LoginRequiredMixin, ListView):
    model = DrugBatch
    template_name = 'inventory/expired_drugs.html'
    context_object_name = 'expired_batches'

    def get_queryset(self):
        from django.utils import timezone
        return DrugBatch.objects.select_related('drug').filter(
            expiry_date__lt=timezone.now().date()
        )

class ExpiringSoonView(LoginRequiredMixin, ListView):
    model = DrugBatch
    template_name = 'inventory/expiring_soon.html'
    context_object_name = 'expiring_batches'

    def get_queryset(self):
        from django.utils import timezone
        from datetime import timedelta

        warning_date = timezone.now().date() + timedelta(days=30)
        return DrugBatch.objects.select_related('drug').filter(
            expiry_date__lte=warning_date,
            expiry_date__gte=timezone.now().date()
        )

class InventoryAdjustmentView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/inventory_adjustment.html'

class InventoryTransferView(LoginRequiredMixin, TemplateView):
    template_name = 'inventory/inventory_transfer.html'

# API Views
def stock_check_api(request):
    """API للتحقق من حالة المخزون"""
    drug_id = request.GET.get('drug_id')
    warehouse_id = request.GET.get('warehouse_id')

    if not drug_id:
        return JsonResponse({'error': 'Drug ID is required'})

    queryset = InventoryItem.objects.filter(drug_id=drug_id)
    if warehouse_id:
        queryset = queryset.filter(warehouse_id=warehouse_id)

    total_stock = queryset.aggregate(total=Sum('quantity'))['total'] or 0
    available_stock = queryset.aggregate(
        available=Sum(F('quantity') - F('reserved_quantity'))
    )['available'] or 0

    return JsonResponse({
        'total_stock': total_stock,
        'available_stock': available_stock,
        'warehouses': list(queryset.values('warehouse__name', 'quantity', 'available_quantity'))
    })

def warehouse_drugs_api(request, warehouse_id):
    """API للحصول على أدوية مستودع معين"""
    items = InventoryItem.objects.filter(
        warehouse_id=warehouse_id,
        quantity__gt=0
    ).select_related('drug')

    drugs = []
    for item in items:
        drugs.append({
            'id': item.drug.id,
            'name': item.drug.commercial_name,
            'quantity': item.quantity,
            'available_quantity': item.available_quantity,
            'unit': item.drug.get_unit_display()
        })

    return JsonResponse({'drugs': drugs})
