# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Institution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المؤسسة')),
                ('institution_type', models.CharField(choices=[('hospital', 'مستشفى'), ('clinic', 'عيادة'), ('health_center', 'مركز صحي'), ('pharmacy', 'صيدلية'), ('laboratory', 'مختبر')], max_length=20, verbose_name='نوع المؤسسة')),
                ('license_number', models.CharField(max_length=100, unique=True, verbose_name='رقم الترخيص')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=100, verbose_name='المدينة')),
                ('phone', models.CharField(max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('contact_person', models.CharField(max_length=200, verbose_name='الشخص المسؤول')),
                ('contact_person_phone', models.CharField(max_length=20, verbose_name='هاتف الشخص المسؤول')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الحد الائتماني')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'المؤسسة الطبية',
                'verbose_name_plural': 'المؤسسات الطبية',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم القسم')),
                ('code', models.CharField(max_length=20, verbose_name='رمز القسم')),
                ('head_of_department', models.CharField(blank=True, max_length=200, verbose_name='رئيس القسم')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='هاتف القسم')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='الموقع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('can_request_drugs', models.BooleanField(default=True, verbose_name='يمكنه طلب الأدوية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('institution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='institutions.institution', verbose_name='المؤسسة')),
            ],
            options={
                'verbose_name': 'القسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['institution', 'name'],
                'unique_together': {('institution', 'code')},
            },
        ),
    ]
