#!/usr/bin/env python
"""
Simple script to install demo data for the Drug Management System
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dms_project.settings')
    django.setup()

def run_command(command):
    """Run a Django management command"""
    print(f"\n{'='*50}")
    print(f"تشغيل الأمر: {command}")
    print(f"{'='*50}")
    
    try:
        execute_from_command_line(['manage.py'] + command.split())
        print(f"✅ تم تنفيذ الأمر بنجاح: {command}")
        return True
    except Exception as e:
        print(f"❌ خطأ في تنفيذ الأمر {command}: {str(e)}")
        return False

def main():
    """Main function to install demo data"""
    print("🚀 بدء تثبيت البيانات التجريبية البسيطة")
    print("="*50)
    
    # Setup Django
    setup_django()
    
    # List of commands to run in order
    commands = [
        "makemigrations",
        "migrate",
        "create_basic_data",
        "add_minimal_data",
    ]
    
    success_count = 0
    total_commands = len(commands)
    
    for command in commands:
        if run_command(command):
            success_count += 1
        else:
            print(f"\n❌ فشل في تنفيذ الأمر: {command}")
            print("سيتم المتابعة مع الأوامر الأخرى...")
    
    print(f"\n{'='*50}")
    print("🎉 انتهى تثبيت البيانات التجريبية!")
    print(f"{'='*50}")
    print(f"✅ تم تنفيذ {success_count}/{total_commands} أوامر بنجاح")
    
    print("\n📊 ملخص البيانات المثبتة:")
    print("-" * 30)
    
    # Import models after Django setup
    try:
        from drugs.models import Drug, DrugCategory, Manufacturer
        from inventory.models import Warehouse, DrugBatch, InventoryItem, InventoryMovement
        from procurement.models import Supplier, PurchaseOrder
        from django.contrib.auth.models import User
        
        print(f"👥 المستخدمين: {User.objects.count()}")
        print(f"🏷️  تصنيفات الأدوية: {DrugCategory.objects.count()}")
        print(f"🏭 الشركات المصنعة: {Manufacturer.objects.count()}")
        print(f"💊 الأدوية: {Drug.objects.count()}")
        print(f"🏢 المستودعات: {Warehouse.objects.count()}")
        print(f"📦 الدفعات: {DrugBatch.objects.count()}")
        print(f"📋 عناصر المخزون: {InventoryItem.objects.count()}")
        print(f"🔄 حركات المخزون: {InventoryMovement.objects.count()}")
        print(f"🚚 الموردين: {Supplier.objects.count()}")
        print(f"🛒 أوامر الشراء: {PurchaseOrder.objects.count()}")
        
        print(f"\n{'='*50}")
        print("🎯 معلومات تسجيل الدخول:")
        print("-" * 25)
        print("المستخدم: admin")
        print("كلمة المرور: admin123")
        print("\nمستخدمين إضافيين:")
        print("- pharmacist1 / password123")
        print("- pharmacist2 / password123")
        print("- manager1 / password123")
        
        print(f"\n{'='*50}")
        print("🌐 روابط مهمة:")
        print("-" * 15)
        print("🏠 لوحة التحكم: http://127.0.0.1:8000/")
        print("💊 الأدوية: http://127.0.0.1:8000/drugs/")
        print("📦 المخزون: http://127.0.0.1:8000/inventory/")
        print("🛒 المشتريات: http://127.0.0.1:8000/procurement/")
        print("📊 التقارير: http://127.0.0.1:8000/reports/")
        
        print(f"\n{'='*50}")
        print("🎉 النظام جاهز للاستخدام!")
        print("يمكنك الآن تشغيل الخادم باستخدام:")
        print("python manage.py runserver")
        print(f"{'='*50}")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    main()
