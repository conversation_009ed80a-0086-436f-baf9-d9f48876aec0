# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('drugs', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المستودع')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='رمز المستودع')),
                ('location', models.CharField(max_length=200, verbose_name='الموقع')),
                ('manager', models.Cha<PERSON><PERSON><PERSON>(max_length=200, verbose_name='مدير المستودع')),
                ('phone', models.CharField(max_length=20, verbose_name='الهاتف')),
                ('capacity', models.PositiveIntegerField(verbose_name='السعة التخزينية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_main_warehouse', models.BooleanField(default=False, verbose_name='مستودع رئيسي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'المستودع',
                'verbose_name_plural': 'المستودعات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='DrugBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=100, verbose_name='رقم الدفعة')),
                ('manufacturing_date', models.DateField(verbose_name='تاريخ التصنيع')),
                ('expiry_date', models.DateField(verbose_name='تاريخ الانتهاء')),
                ('supplier_batch_number', models.CharField(blank=True, max_length=100, verbose_name='رقم دفعة المورد')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر التكلفة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_recalled', models.BooleanField(default=False, verbose_name='مسحوب')),
                ('recall_reason', models.TextField(blank=True, verbose_name='سبب السحب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batches', to='drugs.drug', verbose_name='الدواء')),
            ],
            options={
                'verbose_name': 'دفعة الدواء',
                'verbose_name_plural': 'دفعات الأدوية',
                'ordering': ['expiry_date'],
                'unique_together': {('drug', 'batch_number')},
            },
        ),
        migrations.CreateModel(
            name='InventoryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية الحالية')),
                ('reserved_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المحجوزة')),
                ('minimum_stock', models.PositiveIntegerField(default=10, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock', models.PositiveIntegerField(default=1000, verbose_name='الحد الأقصى للمخزون')),
                ('shelf_location', models.CharField(blank=True, max_length=50, verbose_name='موقع الرف')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_items', to='inventory.drugbatch', verbose_name='الدفعة')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_items', to='drugs.drug', verbose_name='الدواء')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_items', to='inventory.warehouse', verbose_name='المستودع')),
            ],
            options={
                'verbose_name': 'عنصر المخزون',
                'verbose_name_plural': 'عناصر المخزون',
                'ordering': ['warehouse', 'drug'],
                'unique_together': {('warehouse', 'drug', 'batch')},
            },
        ),
        migrations.CreateModel(
            name='InventoryMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'إدخال'), ('out', 'إخراج'), ('transfer', 'نقل'), ('adjustment', 'تسوية'), ('return', 'إرجاع'), ('damage', 'تلف'), ('expiry', 'انتهاء صلاحية')], max_length=20, verbose_name='نوع الحركة')),
                ('quantity', models.IntegerField(verbose_name='الكمية')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('inventory_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.inventoryitem', verbose_name='عنصر المخزون')),
                ('from_warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='outgoing_movements', to='inventory.warehouse', verbose_name='من مستودع')),
                ('to_warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incoming_movements', to='inventory.warehouse', verbose_name='إلى مستودع')),
            ],
            options={
                'verbose_name': 'حركة المخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-created_at'],
            },
        ),
    ]
