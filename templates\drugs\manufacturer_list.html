{% extends 'base/base.html' %}
{% load static %}

{% block title %}الشركات المصنعة - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">الشركات المصنعة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-industry me-2"></i>
                الشركات المصنعة
            </h1>
            <a href="{% url 'drugs:manufacturer_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة شركة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Search -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-8">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ request.GET.search }}" placeholder="البحث في الشركات المصنعة...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <a href="{% url 'drugs:manufacturer_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                مسح
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الشركات المصنعة ({{ manufacturers|length }} شركة)
                </h5>
            </div>
            <div class="card-body">
                {% if manufacturers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم الشركة</th>
                                <th>البلد</th>
                                <th>عدد الأدوية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for manufacturer in manufacturers %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="fas fa-industry"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ manufacturer.name }}</h6>
                                            {% if manufacturer.contact_info %}
                                            <small class="text-muted">{{ manufacturer.contact_info|truncatewords:5 }}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ manufacturer.country }}</span>
                                </td>
                                <td>
                                    <span class="fw-bold text-primary">{{ manufacturer.drug_set.count }}</span>
                                    <small class="text-muted">دواء</small>
                                </td>
                                <td>
                                    {% if manufacturer.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ manufacturer.created_at|date:"d/m/Y" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'drugs:manufacturer_detail' manufacturer.pk %}"
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'drugs:manufacturer_edit' manufacturer.pk %}"
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'drugs:drug_list' %}?manufacturer={{ manufacturer.id }}"
                                           class="btn btn-outline-primary" title="عرض الأدوية">
                                            <i class="fas fa-pills"></i>
                                        </a>
                                        <a href="{% url 'drugs:manufacturer_delete' manufacturer.pk %}"
                                           class="btn btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الشركة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-industry fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد شركات مصنعة</h5>
                    <p class="text-muted">
                        {% if request.GET.search %}
                        لم يتم العثور على شركات تطابق معايير البحث.
                        {% else %}
                        لم يتم إضافة أي شركات مصنعة بعد.
                        {% endif %}
                    </p>
                    {% if not request.GET.search %}
                    <a href="{% url 'drugs:manufacturer_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول شركة
                    </a>
                    {% else %}
                    <a href="{% url 'drugs:manufacturer_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        عرض جميع الشركات
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
{% if manufacturers %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ total_manufacturers }}</h4>
                <small class="text-muted">إجمالي الشركات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">{{ active_manufacturers }}</h4>
                <small class="text-muted">شركات نشطة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">{{ total_countries }}</h4>
                <small class="text-muted">دولة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">{{ total_drugs }}</h4>
                <small class="text-muted">إجمالي الأدوية</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on Enter
document.getElementById('search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});
</script>
{% endblock %}
