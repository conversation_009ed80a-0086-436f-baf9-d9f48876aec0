{% extends 'base/base.html' %}
{% load static %}

{% block title %}إنشاء تقرير مخصص - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">إنشاء تقرير مخصص</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-cogs text-dark me-2"></i>
                إنشاء تقرير مخصص
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'reports:report_template_list' %}" class="btn btn-outline-info">
                    <i class="fas fa-file-alt me-2"></i>
                    القوالب المحفوظة
                </a>
                <button type="button" class="btn btn-success" onclick="generateCustomReport()">
                    <i class="fas fa-play me-2"></i>
                    إنشاء التقرير
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Builder -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>
                    منشئ التقرير
                </h6>
            </div>
            <div class="card-body">
                <form id="customReportForm">
                    <!-- Report Basic Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="reportName" class="form-label">اسم التقرير</label>
                            <input type="text" class="form-control" id="reportName" placeholder="أدخل اسم التقرير">
                        </div>
                        <div class="col-md-6">
                            <label for="reportType" class="form-label">نوع التقرير</label>
                            <select class="form-select" id="reportType" onchange="updateDataSources()">
                                <option value="">اختر نوع التقرير</option>
                                <option value="inventory">تقرير مخزون</option>
                                <option value="orders">تقرير طلبات</option>
                                <option value="financial">تقرير مالي</option>
                                <option value="usage">تقرير استخدام</option>
                                <option value="suppliers">تقرير موردين</option>
                            </select>
                        </div>
                    </div>

                    <!-- Data Sources -->
                    <div class="mb-4">
                        <label class="form-label">مصادر البيانات</label>
                        <div class="row" id="dataSources">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="drugs" value="drugs">
                                    <label class="form-check-label" for="drugs">
                                        <i class="fas fa-pills me-2"></i>
                                        الأدوية
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="inventory" value="inventory">
                                    <label class="form-check-label" for="inventory">
                                        <i class="fas fa-warehouse me-2"></i>
                                        المخزون
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="orders" value="orders">
                                    <label class="form-check-label" for="orders">
                                        <i class="fas fa-file-medical me-2"></i>
                                        الطلبات
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="suppliers" value="suppliers">
                                    <label class="form-check-label" for="suppliers">
                                        <i class="fas fa-truck me-2"></i>
                                        الموردين
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="movements" value="movements">
                                    <label class="form-check-label" for="movements">
                                        <i class="fas fa-exchange-alt me-2"></i>
                                        حركات المخزون
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="batches" value="batches">
                                    <label class="form-check-label" for="batches">
                                        <i class="fas fa-boxes me-2"></i>
                                        الدفعات
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="mb-4">
                        <label class="form-label">الفلاتر</label>
                        <div class="row">
                            <div class="col-md-4">
                                <label for="dateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFrom">
                            </div>
                            <div class="col-md-4">
                                <label for="dateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateTo">
                            </div>
                            <div class="col-md-4">
                                <label for="warehouse" class="form-label">المستودع</label>
                                <select class="form-select" id="warehouse">
                                    <option value="">جميع المستودعات</option>
                                    <option value="1">المستودع الرئيسي</option>
                                    <option value="2">مستودع الطوارئ</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Fields Selection -->
                    <div class="mb-4">
                        <label class="form-label">الحقول المطلوبة</label>
                        <div class="row" id="fieldsSelection">
                            <!-- Fields will be populated based on data sources -->
                        </div>
                    </div>

                    <!-- Grouping and Sorting -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="groupBy" class="form-label">تجميع حسب</label>
                            <select class="form-select" id="groupBy">
                                <option value="">بدون تجميع</option>
                                <option value="category">التصنيف</option>
                                <option value="warehouse">المستودع</option>
                                <option value="supplier">المورد</option>
                                <option value="date">التاريخ</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="sortBy" class="form-label">ترتيب حسب</label>
                            <select class="form-select" id="sortBy">
                                <option value="name">الاسم</option>
                                <option value="date">التاريخ</option>
                                <option value="quantity">الكمية</option>
                                <option value="value">القيمة</option>
                            </select>
                        </div>
                    </div>

                    <!-- Chart Options -->
                    <div class="mb-4">
                        <label class="form-label">خيارات الرسوم البيانية</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeCharts">
                                    <label class="form-check-label" for="includeCharts">
                                        تضمين رسوم بيانية
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <select class="form-select" id="chartType" disabled>
                                    <option value="bar">رسم عمودي</option>
                                    <option value="line">رسم خطي</option>
                                    <option value="pie">رسم دائري</option>
                                    <option value="doughnut">رسم دائري مفرغ</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Save Template -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="saveTemplate">
                            <label class="form-check-label" for="saveTemplate">
                                حفظ كقالب للاستخدام المستقبلي
                            </label>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Preview Panel -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة التقرير
                </h6>
            </div>
            <div class="card-body">
                <div id="reportPreview" class="text-center text-muted">
                    <i class="fas fa-file-alt fa-3x mb-3"></i>
                    <p>اختر مصادر البيانات والحقول لمعاينة التقرير</p>
                </div>
            </div>
        </div>

        <!-- Quick Templates -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    قوالب سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="loadQuickTemplate('inventory_summary')">
                        <i class="fas fa-warehouse me-2"></i>
                        ملخص المخزون
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="loadQuickTemplate('monthly_orders')">
                        <i class="fas fa-calendar me-2"></i>
                        الطلبات الشهرية
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="loadQuickTemplate('supplier_performance')">
                        <i class="fas fa-star me-2"></i>
                        أداء الموردين
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="loadQuickTemplate('financial_summary')">
                        <i class="fas fa-dollar-sign me-2"></i>
                        الملخص المالي
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enable/disable chart type based on include charts checkbox
    document.getElementById('includeCharts').addEventListener('change', function() {
        document.getElementById('chartType').disabled = !this.checked;
    });

    // Update preview when form changes
    document.getElementById('customReportForm').addEventListener('change', updatePreview);
});

function updateDataSources() {
    const reportType = document.getElementById('reportType').value;
    const dataSources = document.getElementById('dataSources');
    
    // Reset all checkboxes
    dataSources.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
    
    // Auto-select relevant data sources based on report type
    switch(reportType) {
        case 'inventory':
            document.getElementById('drugs').checked = true;
            document.getElementById('inventory').checked = true;
            document.getElementById('batches').checked = true;
            break;
        case 'orders':
            document.getElementById('orders').checked = true;
            document.getElementById('drugs').checked = true;
            break;
        case 'financial':
            document.getElementById('orders').checked = true;
            document.getElementById('suppliers').checked = true;
            document.getElementById('inventory').checked = true;
            break;
        case 'usage':
            document.getElementById('orders').checked = true;
            document.getElementById('movements').checked = true;
            break;
        case 'suppliers':
            document.getElementById('suppliers').checked = true;
            document.getElementById('orders').checked = true;
            break;
    }
    
    updateFieldsSelection();
    updatePreview();
}

function updateFieldsSelection() {
    const fieldsContainer = document.getElementById('fieldsSelection');
    const checkedSources = Array.from(document.querySelectorAll('#dataSources input:checked')).map(cb => cb.value);
    
    fieldsContainer.innerHTML = '';
    
    const fieldsBySource = {
        drugs: ['اسم الدواء', 'التصنيف', 'الشركة المصنعة', 'الوحدة'],
        inventory: ['الكمية المتاحة', 'الحد الأدنى', 'القيمة'],
        orders: ['رقم الطلب', 'تاريخ الطلب', 'الحالة', 'المؤسسة'],
        suppliers: ['اسم المورد', 'نوع المورد', 'التقييم'],
        movements: ['نوع الحركة', 'الكمية', 'التاريخ'],
        batches: ['رقم الدفعة', 'تاريخ الانتهاء', 'تاريخ الإنتاج']
    };
    
    checkedSources.forEach(source => {
        if (fieldsBySource[source]) {
            fieldsBySource[source].forEach(field => {
                const div = document.createElement('div');
                div.className = 'col-md-6 mb-2';
                div.innerHTML = `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="field_${field}" value="${field}">
                        <label class="form-check-label" for="field_${field}">
                            ${field}
                        </label>
                    </div>
                `;
                fieldsContainer.appendChild(div);
            });
        }
    });
}

function updatePreview() {
    const reportName = document.getElementById('reportName').value;
    const reportType = document.getElementById('reportType').value;
    const checkedSources = Array.from(document.querySelectorAll('#dataSources input:checked')).map(cb => cb.value);
    const checkedFields = Array.from(document.querySelectorAll('#fieldsSelection input:checked')).map(cb => cb.value);
    
    const preview = document.getElementById('reportPreview');
    
    if (reportName && reportType && checkedSources.length > 0) {
        preview.innerHTML = `
            <div class="text-start">
                <h6 class="text-primary">${reportName}</h6>
                <p class="small mb-2"><strong>النوع:</strong> ${getReportTypeLabel(reportType)}</p>
                <p class="small mb-2"><strong>مصادر البيانات:</strong> ${checkedSources.length} مصدر</p>
                <p class="small mb-2"><strong>الحقول:</strong> ${checkedFields.length} حقل</p>
                <div class="mt-3">
                    <div class="bg-light p-2 rounded">
                        <small class="text-muted">معاينة البيانات...</small>
                        <div class="table-responsive mt-2">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        ${checkedFields.slice(0, 3).map(field => `<th class="small">${field}</th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="small">بيانات تجريبية</td>
                                        <td class="small">قيمة 1</td>
                                        <td class="small">قيمة 2</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        preview.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-file-alt fa-3x mb-3"></i>
                <p>اختر مصادر البيانات والحقول لمعاينة التقرير</p>
            </div>
        `;
    }
}

function getReportTypeLabel(type) {
    const labels = {
        inventory: 'تقرير مخزون',
        orders: 'تقرير طلبات',
        financial: 'تقرير مالي',
        usage: 'تقرير استخدام',
        suppliers: 'تقرير موردين'
    };
    return labels[type] || type;
}

function loadQuickTemplate(templateType) {
    // Reset form
    document.getElementById('customReportForm').reset();
    
    switch(templateType) {
        case 'inventory_summary':
            document.getElementById('reportName').value = 'ملخص المخزون';
            document.getElementById('reportType').value = 'inventory';
            updateDataSources();
            break;
        case 'monthly_orders':
            document.getElementById('reportName').value = 'الطلبات الشهرية';
            document.getElementById('reportType').value = 'orders';
            updateDataSources();
            break;
        case 'supplier_performance':
            document.getElementById('reportName').value = 'أداء الموردين';
            document.getElementById('reportType').value = 'suppliers';
            updateDataSources();
            break;
        case 'financial_summary':
            document.getElementById('reportName').value = 'الملخص المالي';
            document.getElementById('reportType').value = 'financial';
            updateDataSources();
            break;
    }
    
    showToast(`تم تحميل قالب ${getReportTypeLabel(templateType)}`, 'success');
}

function generateCustomReport() {
    const reportName = document.getElementById('reportName').value;
    const reportType = document.getElementById('reportType').value;
    
    if (!reportName || !reportType) {
        showToast('يرجى إدخال اسم التقرير واختيار النوع', 'warning');
        return;
    }
    
    const checkedSources = Array.from(document.querySelectorAll('#dataSources input:checked')).map(cb => cb.value);
    if (checkedSources.length === 0) {
        showToast('يرجى اختيار مصدر بيانات واحد على الأقل', 'warning');
        return;
    }
    
    // Show loading
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
    button.disabled = true;
    
    // Simulate report generation
    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        showToast(`تم إنشاء التقرير "${reportName}" بنجاح`, 'success');
        
        // Save template if requested
        if (document.getElementById('saveTemplate').checked) {
            showToast('تم حفظ القالب للاستخدام المستقبلي', 'info');
        }
    }, 3000);
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
