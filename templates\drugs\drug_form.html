{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل الدواء{% else %}إضافة دواء جديد{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:drug_list' %}">قائمة الأدوية</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل الدواء{% else %}إضافة دواء جديد{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-pills me-2"></i>
            {% if object %}تعديل الدواء: {{ object.commercial_name }}{% else %}إضافة دواء جديد{% endif %}
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">المعلومات الأساسية</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.commercial_name.id_for_label }}" class="form-label">
                                الاسم التجاري <span class="text-danger">*</span>
                            </label>
                            {{ form.commercial_name }}
                            {% if form.commercial_name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.commercial_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.scientific_name.id_for_label }}" class="form-label">
                                الاسم العلمي <span class="text-danger">*</span>
                            </label>
                            {{ form.scientific_name }}
                            {% if form.scientific_name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.scientific_name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.category.id_for_label }}" class="form-label">
                                التصنيف <span class="text-danger">*</span>
                            </label>
                            {{ form.category }}
                            {% if form.category.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.category.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.manufacturer.id_for_label }}" class="form-label">
                                الشركة المصنعة <span class="text-danger">*</span>
                            </label>
                            {{ form.manufacturer }}
                            {% if form.manufacturer.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.manufacturer.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.barcode.id_for_label }}" class="form-label">
                                الباركود
                            </label>
                            {{ form.barcode }}
                            {% if form.barcode.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.barcode.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Product Details -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">تفاصيل المنتج</h6>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.unit.id_for_label }}" class="form-label">
                                الوحدة <span class="text-danger">*</span>
                            </label>
                            {{ form.unit }}
                            {% if form.unit.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.unit.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.unit_price.id_for_label }}" class="form-label">
                                سعر الوحدة (ريال) <span class="text-danger">*</span>
                            </label>
                            {{ form.unit_price }}
                            {% if form.unit_price.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.unit_price.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="{{ form.strength.id_for_label }}" class="form-label">
                                التركيز
                            </label>
                            {{ form.strength }}
                            {% if form.strength.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.strength.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.dosage_form.id_for_label }}" class="form-label">
                                الشكل الصيدلاني
                            </label>
                            {{ form.dosage_form }}
                            {% if form.dosage_form.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.dosage_form.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                الوصف
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Settings -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">الإعدادات</h6>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    نشط
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                {{ form.requires_prescription }}
                                <label class="form-check-label" for="{{ form.requires_prescription.id_for_label }}">
                                    يتطلب وصفة طبية
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                {{ form.is_controlled }}
                                <label class="form-check-label" for="{{ form.is_controlled.id_for_label }}">
                                    دواء مراقب
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'drugs:drug_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    {% if object %}تحديث الدواء{% else %}إضافة الدواء{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">نصائح:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من صحة الاسم العلمي والتجاري</li>
                        <li>اختر التصنيف المناسب للدواء</li>
                        <li>تأكد من دقة سعر الوحدة</li>
                        <li>حدد ما إذا كان الدواء يتطلب وصفة طبية</li>
                        <li>الأدوية المراقبة تحتاج موافقات خاصة</li>
                    </ul>
                </div>
                
                {% if object %}
                <div class="alert alert-warning">
                    <h6 class="alert-heading">تحذير:</h6>
                    <p class="mb-0 small">
                        تعديل معلومات الدواء قد يؤثر على الطلبات والمخزون الحالي.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation and enhancements
document.addEventListener('DOMContentLoaded', function() {
    // Auto-format price input
    const priceInput = document.getElementById('{{ form.unit_price.id_for_label }}');
    if (priceInput) {
        priceInput.addEventListener('input', function() {
            let value = this.value.replace(/[^\d.]/g, '');
            if (value.split('.').length > 2) {
                value = value.substring(0, value.lastIndexOf('.'));
            }
            this.value = value;
        });
    }
});
</script>
{% endblock %}
