{% extends 'base/base.html' %}
{% load static %}

{% block title %}إدارة المخزون - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">إدارة المخزون</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-warehouse me-2"></i>
                إدارة المخزون
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:movement_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    حركة مخزون جديدة
                </a>
                <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-outline-info">
                    <i class="fas fa-building me-2"></i>
                    المستودعات
                </a>
                <a href="{% url 'inventory:batch_list' %}" class="btn btn-outline-warning">
                    <i class="fas fa-boxes me-2"></i>
                    الدفعات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ inventory_items|length }}</h4>
                <small class="text-muted">عنصر مخزون</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with low_stock=0 %}
                        {% for item in inventory_items %}
                            {% if item.is_low_stock %}
                                {% with low_stock=low_stock|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ low_stock }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مخزون منخفض</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with out_of_stock=0 %}
                        {% for item in inventory_items %}
                            {% if item.is_out_of_stock %}
                                {% with out_of_stock=out_of_stock|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ out_of_stock }}
                    {% endwith %}
                </h4>
                <small class="text-muted">نفد المخزون</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">{{ warehouses|length }}</h4>
                <small class="text-muted">مستودع</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-6">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.GET.search }}" placeholder="البحث في الأدوية...">
                    </div>
                    <div class="col-md-4">
                        <label for="warehouse" class="form-label">المستودع</label>
                        <select class="form-select" id="warehouse" name="warehouse">
                            <option value="">جميع المستودعات</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if request.GET.warehouse == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                {{ warehouse.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:low_stock' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            المخزون المنخفض
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:expired_drugs' %}" class="btn btn-outline-danger w-100">
                            <i class="fas fa-clock me-2"></i>
                            الأدوية المنتهية
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:expiring_soon' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تنتهي قريباً
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{% url 'inventory:movement_list' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-history me-2"></i>
                            حركات المخزون
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Items -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    عناصر المخزون ({{ inventory_items|length }} عنصر)
                </h5>
            </div>
            <div class="card-body">
                {% if inventory_items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الدفعة</th>
                                <th>الكمية المتاحة</th>
                                <th>الكمية المحجوزة</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in inventory_items %}
                            <tr {% if item.is_out_of_stock %}class="table-danger"{% elif item.is_low_stock %}class="table-warning"{% endif %}>
                                <td>
                                    <div>
                                        <strong>{{ item.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.drug.scientific_name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ item.warehouse.name }}</span>
                                    {% if item.shelf_location %}
                                    <br><small class="text-muted">{{ item.shelf_location }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <code>{{ item.batch.batch_number }}</code>
                                </td>
                                <td>
                                    <span class="fw-bold {% if item.is_out_of_stock %}text-danger{% elif item.is_low_stock %}text-warning{% else %}text-success{% endif %}">
                                        {{ item.available_quantity }}
                                    </span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                </td>
                                <td>
                                    {% if item.reserved_quantity > 0 %}
                                    <span class="text-warning">{{ item.reserved_quantity }}</span>
                                    {% else %}
                                    <span class="text-muted">0</span>
                                    {% endif %}
                                </td>
                                <td>{{ item.minimum_stock }}</td>
                                <td>
                                    {% if item.is_out_of_stock %}
                                    <span class="badge bg-danger">نفد المخزون</span>
                                    {% elif item.is_low_stock %}
                                    <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                    {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="{% if item.batch.is_expired %}text-danger{% elif item.batch.days_to_expiry <= 30 %}text-warning{% endif %}">
                                        {{ item.batch.expiry_date|date:"d/m/Y" }}
                                    </span>
                                    {% if item.batch.is_expired %}
                                    <br><small class="text-danger">منتهي</small>
                                    {% elif item.batch.days_to_expiry <= 30 %}
                                    <br><small class="text-warning">{{ item.batch.days_to_expiry }} يوم</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:inventory_item_detail' item.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:inventory_item_update' item.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'inventory:movement_create' %}?item={{ item.pk }}" 
                                           class="btn btn-outline-primary" title="حركة مخزون">
                                            <i class="fas fa-exchange-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.warehouse %}&warehouse={{ request.GET.warehouse }}{% endif %}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عناصر مخزون</h5>
                    <p class="text-muted">
                        {% if request.GET.search or request.GET.warehouse %}
                        لم يتم العثور على عناصر تطابق معايير البحث.
                        {% else %}
                        لم يتم إضافة أي عناصر للمخزون بعد.
                        {% endif %}
                    </p>
                    {% if not request.GET.search and not request.GET.warehouse %}
                    <a href="{% url 'inventory:batch_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دفعة جديدة
                    </a>
                    {% else %}
                    <a href="{% url 'inventory:inventory_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        عرض جميع العناصر
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on Enter
document.getElementById('search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});

// Auto-submit on warehouse change
document.getElementById('warehouse').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
