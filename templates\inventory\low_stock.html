{% extends 'base/base.html' %}
{% load static %}

{% block title %}المخزون المنخفض - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item active">المخزون المنخفض</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                تنبيهات المخزون المنخفض
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:movement_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مخزون
                </a>
                <a href="{% url 'inventory:inventory_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للمخزون
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Alert Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تحذير: مخزون منخفض
            </h5>
            <p class="mb-0">
                يوجد <strong>{{ low_stock_items|length }}</strong> عنصر مخزون وصل إلى الحد الأدنى أو أقل. 
                يُنصح بإعادة تعبئة هذه الأدوية في أقرب وقت ممكن لتجنب نفاد المخزون.
            </p>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">{{ low_stock_items|length }}</h4>
                <small class="text-muted">عنصر مخزون منخفض</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with critical_count=0 %}
                        {% for item in low_stock_items %}
                            {% if item.available_quantity <= 0 %}
                                {% with critical_count=critical_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ critical_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">نفد المخزون</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% with warehouses=low_stock_items|regroup:"warehouse" %}
                    {{ warehouses|length }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مستودع متأثر</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">
                    {% with categories=low_stock_items|regroup:"drug.category" %}
                    {{ categories|length }}
                    {% endwith %}
                </h4>
                <small class="text-muted">تصنيف متأثر</small>
            </div>
        </div>
    </div>
</div>

<!-- Low Stock Items -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الأدوية ذات المخزون المنخفض
                </h5>
            </div>
            <div class="card-body">
                {% if low_stock_items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الأولوية</th>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>النقص</th>
                                <th>آخر حركة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in low_stock_items %}
                            <tr class="{% if item.available_quantity <= 0 %}table-danger{% else %}table-warning{% endif %}">
                                <td>
                                    {% if item.available_quantity <= 0 %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        حرج
                                    </span>
                                    {% elif item.available_quantity <= item.minimum_stock|floatformat:0|mul:0.5 %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        عالي
                                    </span>
                                    {% else %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-info-circle me-1"></i>
                                        متوسط
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ item.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.drug.scientific_name }}</small>
                                        <br>
                                        <span class="badge bg-primary">{{ item.drug.category.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ item.warehouse.name }}</span>
                                    {% if item.shelf_location %}
                                    <br><small class="text-muted">{{ item.shelf_location }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="fw-bold {% if item.available_quantity <= 0 %}text-danger{% else %}text-warning{% endif %}">
                                        {{ item.available_quantity }}
                                    </span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                    {% if item.reserved_quantity > 0 %}
                                    <br><small class="text-warning">محجوز: {{ item.reserved_quantity }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-info fw-bold">{{ item.minimum_stock }}</span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                </td>
                                <td>
                                    {% with shortage=item.minimum_stock|sub:item.available_quantity %}
                                    {% if shortage > 0 %}
                                    <span class="text-danger fw-bold">{{ shortage }}</span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                    {% else %}
                                    <span class="text-success">-</span>
                                    {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    {% with last_movement=item.movements.first %}
                                    {% if last_movement %}
                                    <div>
                                        <small class="text-muted">{{ last_movement.created_at|date:"d/m/Y" }}</small>
                                        <br>
                                        <span class="badge bg-secondary">{{ last_movement.get_movement_type_display }}</span>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">لا توجد حركات</span>
                                    {% endif %}
                                    {% endwith %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:movement_create' %}?item={{ item.pk }}&type=in" 
                                           class="btn btn-success" title="إضافة مخزون">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <a href="{% url 'inventory:inventory_item_detail' item.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:inventory_item_update' item.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل الحدود">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">إجراءات سريعة:</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-success" onclick="bulkAddStock()">
                                        <i class="fas fa-plus me-2"></i>
                                        إضافة مخزون للكل
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="generatePurchaseOrder()">
                                        <i class="fas fa-shopping-cart me-2"></i>
                                        إنشاء أمر شراء
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="exportLowStockReport()">
                                        <i class="fas fa-file-export me-2"></i>
                                        تصدير التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد تنبيهات مخزون منخفض</h5>
                    <p class="text-muted">جميع عناصر المخزون فوق الحد الأدنى المطلوب.</p>
                    <a href="{% url 'inventory:inventory_list' %}" class="btn btn-primary">
                        <i class="fas fa-warehouse me-2"></i>
                        عرض المخزون
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function bulkAddStock() {
    alert('سيتم تطوير هذه الميزة قريباً - إضافة مخزون للعناصر المحددة');
}

function generatePurchaseOrder() {
    alert('سيتم تطوير هذه الميزة قريباً - إنشاء أمر شراء للأدوية ذات المخزون المنخفض');
}

function exportLowStockReport() {
    alert('سيتم تطوير هذه الميزة قريباً - تصدير تقرير المخزون المنخفض');
}

// Add custom filter for subtraction
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
