# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('drugs', '0001_initial'),
        ('institutions', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DrugOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('partially_fulfilled', 'مكتمل جزئياً'), ('fulfilled', 'مكتمل'), ('rejected', 'مرفوض'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('normal', 'عادي'), ('high', 'عالي'), ('urgent', 'طارئ')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('delivery_address', models.TextField(verbose_name='عنوان التسليم')),
                ('expected_delivery_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_orders', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='institutions.department', verbose_name='القسم')),
                ('institution', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drug_orders', to='institutions.institution', verbose_name='المؤسسة')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='requested_orders', to=settings.AUTH_USER_MODEL, verbose_name='طلب بواسطة')),
            ],
            options={
                'verbose_name': 'طلب صرف الأدوية',
                'verbose_name_plural': 'طلبات صرف الأدوية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='InternalRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('request_type', models.CharField(choices=[('routine', 'روتيني'), ('urgent', 'طارئ'), ('emergency', 'طوارئ')], default='routine', max_length=20, verbose_name='نوع الطلب')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('partially_fulfilled', 'مكتمل جزئياً'), ('fulfilled', 'مكتمل'), ('rejected', 'مرفوض'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='حالة الطلب')),
                ('justification', models.TextField(verbose_name='المبرر')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('needed_by', models.DateTimeField(verbose_name='مطلوب بتاريخ')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_internal_requests', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='internal_requests', to='institutions.department', verbose_name='القسم')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='internal_requests', to=settings.AUTH_USER_MODEL, verbose_name='طلب بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.warehouse', verbose_name='المستودع')),
            ],
            options={
                'verbose_name': 'الطلب الداخلي',
                'verbose_name_plural': 'الطلبات الداخلية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DrugOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requested_quantity', models.PositiveIntegerField(verbose_name='الكمية المطلوبة')),
                ('approved_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية الموافق عليها')),
                ('delivered_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المسلمة')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر الوحدة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='drugs.drug', verbose_name='الدواء')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.drugorder', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'عنصر طلب الصرف',
                'verbose_name_plural': 'عناصر طلب الصرف',
                'unique_together': {('order', 'drug')},
            },
        ),
        migrations.CreateModel(
            name='InternalRequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('requested_quantity', models.PositiveIntegerField(verbose_name='الكمية المطلوبة')),
                ('approved_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية الموافق عليها')),
                ('delivered_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المسلمة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='drugs.drug', verbose_name='الدواء')),
                ('request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.internalrequest', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'عنصر الطلب الداخلي',
                'verbose_name_plural': 'عناصر الطلب الداخلي',
                'unique_together': {('request', 'drug')},
            },
        ),
    ]
