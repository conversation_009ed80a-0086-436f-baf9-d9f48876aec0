from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from drugs.models import DrugCategory, Manufacturer, Drug
from institutions.models import Institution, Department
from inventory.models import Warehouse, DrugBatch, InventoryItem
from accounts.models import UserProfile
from decimal import Decimal

class Command(BaseCommand):
    help = 'Create sample data for testing the DMS system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample data...'))
        
        # Create drug categories
        categories = [
            {'name': 'مضادات حيوية', 'description': 'الأدوية المضادة للبكتيريا'},
            {'name': 'مسكنات الألم', 'description': 'أدوية تسكين الألم والالتهابات'},
            {'name': 'أدوية القلب', 'description': 'أدوية أمراض القلب والأوعية الدموية'},
            {'name': 'أدوية السكري', 'description': 'أدوية علاج مرض السكري'},
            {'name': 'أدوية الضغط', 'description': 'أدوية علاج ضغط الدم'},
            {'name': 'فيتامينات', 'description': 'المكملات الغذائية والفيتامينات'},
        ]
        
        for cat_data in categories:
            category, created = DrugCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create manufacturers
        manufacturers = [
            {'name': 'شركة الدواء السعودية', 'country': 'السعودية'},
            {'name': 'فايزر', 'country': 'الولايات المتحدة'},
            {'name': 'نوفارتيس', 'country': 'سويسرا'},
            {'name': 'روش', 'country': 'سويسرا'},
            {'name': 'جلاكسو سميث كلاين', 'country': 'المملكة المتحدة'},
            {'name': 'سانوفي', 'country': 'فرنسا'},
        ]
        
        for man_data in manufacturers:
            manufacturer, created = Manufacturer.objects.get_or_create(
                name=man_data['name'],
                defaults={'country': man_data['country']}
            )
            if created:
                self.stdout.write(f'Created manufacturer: {manufacturer.name}')

        # Create sample drugs
        drugs_data = [
            {
                'scientific_name': 'Amoxicillin',
                'commercial_name': 'أموكسيسيلين',
                'category': 'مضادات حيوية',
                'manufacturer': 'شركة الدواء السعودية',
                'unit': 'capsule',
                'unit_price': Decimal('15.50'),
                'strength': '500mg'
            },
            {
                'scientific_name': 'Paracetamol',
                'commercial_name': 'بانادول',
                'category': 'مسكنات الألم',
                'manufacturer': 'جلاكسو سميث كلاين',
                'unit': 'tablet',
                'unit_price': Decimal('8.75'),
                'strength': '500mg'
            },
            {
                'scientific_name': 'Metformin',
                'commercial_name': 'جلوكوفاج',
                'category': 'أدوية السكري',
                'manufacturer': 'سانوفي',
                'unit': 'tablet',
                'unit_price': Decimal('12.25'),
                'strength': '850mg'
            },
            {
                'scientific_name': 'Amlodipine',
                'commercial_name': 'نورفاسك',
                'category': 'أدوية الضغط',
                'manufacturer': 'فايزر',
                'unit': 'tablet',
                'unit_price': Decimal('18.00'),
                'strength': '5mg'
            },
            {
                'scientific_name': 'Atorvastatin',
                'commercial_name': 'ليبيتور',
                'category': 'أدوية القلب',
                'manufacturer': 'فايزر',
                'unit': 'tablet',
                'unit_price': Decimal('25.50'),
                'strength': '20mg'
            },
        ]
        
        for drug_data in drugs_data:
            category = DrugCategory.objects.get(name=drug_data['category'])
            manufacturer = Manufacturer.objects.get(name=drug_data['manufacturer'])
            
            drug, created = Drug.objects.get_or_create(
                scientific_name=drug_data['scientific_name'],
                commercial_name=drug_data['commercial_name'],
                manufacturer=manufacturer,
                defaults={
                    'category': category,
                    'unit': drug_data['unit'],
                    'unit_price': drug_data['unit_price'],
                    'strength': drug_data['strength'],
                }
            )
            if created:
                self.stdout.write(f'Created drug: {drug.commercial_name}')

        # Create institutions
        institutions_data = [
            {
                'name': 'مستشفى الملك فهد الجامعي',
                'institution_type': 'hospital',
                'license_number': 'H001',
                'address': 'الخبر، المنطقة الشرقية',
                'city': 'الخبر',
                'phone': '***********',
                'contact_person': 'د. أحمد محمد',
                'contact_person_phone': '***********'
            },
            {
                'name': 'مركز الرعاية الصحية الأولية',
                'institution_type': 'health_center',
                'license_number': 'HC001',
                'address': 'الدمام، المنطقة الشرقية',
                'city': 'الدمام',
                'phone': '***********',
                'contact_person': 'د. فاطمة علي',
                'contact_person_phone': '***********'
            },
        ]
        
        for inst_data in institutions_data:
            institution, created = Institution.objects.get_or_create(
                name=inst_data['name'],
                defaults=inst_data
            )
            if created:
                self.stdout.write(f'Created institution: {institution.name}')

        # Create departments
        departments_data = [
            {'institution': 'مستشفى الملك فهد الجامعي', 'name': 'قسم الطوارئ', 'code': 'ER'},
            {'institution': 'مستشفى الملك فهد الجامعي', 'name': 'قسم الباطنة', 'code': 'INT'},
            {'institution': 'مستشفى الملك فهد الجامعي', 'name': 'قسم الجراحة', 'code': 'SUR'},
            {'institution': 'مركز الرعاية الصحية الأولية', 'name': 'العيادات العامة', 'code': 'GEN'},
        ]
        
        for dept_data in departments_data:
            institution = Institution.objects.get(name=dept_data['institution'])
            department, created = Department.objects.get_or_create(
                institution=institution,
                code=dept_data['code'],
                defaults={'name': dept_data['name']}
            )
            if created:
                self.stdout.write(f'Created department: {department.name}')

        # Create warehouses
        warehouses_data = [
            {
                'name': 'المستودع الرئيسي',
                'code': 'MAIN',
                'location': 'المبنى الرئيسي - الطابق الأرضي',
                'manager': 'أحمد السالم',
                'phone': '013-8966668',
                'capacity': 10000,
                'is_main_warehouse': True
            },
            {
                'name': 'مستودع الطوارئ',
                'code': 'EMER',
                'location': 'قسم الطوارئ',
                'manager': 'سارة محمد',
                'phone': '013-8966669',
                'capacity': 2000,
                'is_main_warehouse': False
            },
        ]
        
        for wh_data in warehouses_data:
            warehouse, created = Warehouse.objects.get_or_create(
                code=wh_data['code'],
                defaults=wh_data
            )
            if created:
                self.stdout.write(f'Created warehouse: {warehouse.name}')

        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))
