from django.urls import path
from . import views

app_name = 'returns'

urlpatterns = [
    # Drug Return URLs
    path('', views.DrugReturnListView.as_view(), name='drug_return_list'),
    path('create/', views.DrugReturnCreateView.as_view(), name='drug_return_create'),
    path('<int:pk>/', views.DrugReturnDetailView.as_view(), name='drug_return_detail'),
    path('<int:pk>/edit/', views.DrugReturnUpdateView.as_view(), name='drug_return_edit'),
    path('<int:pk>/delete/', views.DrugReturnDeleteView.as_view(), name='drug_return_delete'),
    path('<int:pk>/approve/', views.approve_drug_return, name='drug_return_approve'),
    path('<int:pk>/reject/', views.reject_drug_return, name='drug_return_reject'),
    path('<int:pk>/process/', views.process_drug_return, name='drug_return_process'),
    
    # Damaged Drug URLs
    path('damaged/', views.DamagedDrugListView.as_view(), name='damaged_drug_list'),
    path('damaged/create/', views.DamagedDrugCreateView.as_view(), name='damaged_drug_create'),
    path('damaged/<int:pk>/', views.DamagedDrugDetailView.as_view(), name='damaged_drug_detail'),
    path('damaged/<int:pk>/edit/', views.DamagedDrugUpdateView.as_view(), name='damaged_drug_edit'),
    path('damaged/<int:pk>/delete/', views.DamagedDrugDeleteView.as_view(), name='damaged_drug_delete'),
    path('damaged/<int:pk>/dispose/', views.dispose_damaged_drug, name='damaged_drug_dispose'),
    
    # Return Item Management
    path('<int:return_id>/items/add/', views.add_return_item, name='add_return_item'),
    path('items/<int:item_id>/edit/', views.edit_return_item, name='edit_return_item'),
    path('items/<int:item_id>/delete/', views.delete_return_item, name='delete_return_item'),
    
    # Reports
    path('reports/returns-summary/', views.ReturnsSummaryReportView.as_view(), name='returns_summary_report'),
    path('reports/damaged-drugs/', views.DamagedDrugsReportView.as_view(), name='damaged_drugs_report'),
    
    # API URLs
    path('api/return-reasons/', views.return_reasons_api, name='return_reasons_api'),
]
