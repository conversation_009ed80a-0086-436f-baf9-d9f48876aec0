{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل المستودع{% else %}إضافة مستودع جديد{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">المستودعات</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل المستودع{% else %}إضافة مستودع جديد{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    {% if object %}تعديل المستودع: {{ object.name }}{% else %}إضافة مستودع جديد{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="warehouseForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">المعلومات الأساسية</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                اسم المستودع <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أدخل اسماً واضحاً ومميزاً للمستودع
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.code.id_for_label }}" class="form-label">
                                رمز المستودع <span class="text-danger">*</span>
                            </label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.code.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                رمز مختصر للمستودع (مثل: WH001, MAIN, EMRG)
                            </div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                الوصف
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                وصف مختصر للمستودع ونوع الأدوية المخزنة فيه (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Location Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات الموقع</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.location.id_for_label }}" class="form-label">
                                الموقع <span class="text-danger">*</span>
                            </label>
                            {{ form.location }}
                            {% if form.location.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.location.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                العنوان الكامل للمستودع (المبنى، الطابق، الغرفة)
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.capacity.id_for_label }}" class="form-label">
                                السعة التخزينية
                            </label>
                            {{ form.capacity }}
                            {% if form.capacity.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.capacity.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                السعة القصوى للمستودع (بالمتر المكعب أو عدد الوحدات)
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.temperature_controlled.id_for_label }}" class="form-label">
                                التحكم في درجة الحرارة
                            </label>
                            <div class="form-check">
                                {{ form.temperature_controlled }}
                                <label class="form-check-label" for="{{ form.temperature_controlled.id_for_label }}">
                                    مستودع مكيف ومتحكم في درجة الحرارة
                                </label>
                            </div>
                            <div class="form-text">
                                فعل هذا الخيار للمستودعات التي تحتاج تبريد خاص
                            </div>
                        </div>
                    </div>
                    
                    <!-- Management Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات الإدارة</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.manager.id_for_label }}" class="form-label">
                                مدير المستودع
                            </label>
                            {{ form.manager }}
                            {% if form.manager.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.manager.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                اسم الشخص المسؤول عن إدارة المستودع
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                رقم الهاتف
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.phone.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                رقم هاتف المستودع أو المدير المسؤول
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                البريد الإلكتروني
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                البريد الإلكتروني للتواصل مع إدارة المستودع
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status and Settings -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">الحالة والإعدادات</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    مستودع نشط
                                </label>
                                <div class="form-text">
                                    المستودعات النشطة فقط تظهر في قوائم الاختيار
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_main_warehouse }}
                                <label class="form-check-label" for="{{ form.is_main_warehouse.id_for_label }}">
                                    مستودع رئيسي
                                </label>
                                <div class="form-text">
                                    المستودع الرئيسي له أولوية في العمليات
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}تحديث المستودع{% else %}إضافة المستودع{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">إرشادات إنشاء المستودعات:</h6>
                    <ul class="mb-0 small">
                        <li>استخدم أسماء واضحة ومفهومة للمستودعات</li>
                        <li>اختر رموزاً مختصرة وسهلة التذكر</li>
                        <li>حدد الموقع بدقة لسهولة الوصول</li>
                        <li>أضف معلومات الاتصال الصحيحة</li>
                        <li>فعل التحكم في الحرارة للأدوية الحساسة</li>
                        <li>حدد مستودعاً واحداً كرئيسي فقط</li>
                    </ul>
                </div>
                
                {% if object %}
                <div class="alert alert-warning">
                    <h6 class="alert-heading">تحذير:</h6>
                    <p class="mb-0 small">
                        تعديل معلومات المستودع قد يؤثر على عمليات المخزون الجارية.
                        تأكد من صحة البيانات قبل الحفظ.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Warehouse Types -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    أنواع المستودعات
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-star"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مستودع رئيسي</h6>
                                <small class="text-muted">المستودع الأساسي للمؤسسة</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2">
                                <i class="fas fa-ambulance"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مستودع الطوارئ</h6>
                                <small class="text-muted">للأدوية العاجلة والطوارئ</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">
                                <i class="fas fa-snowflake"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مستودع مبرد</h6>
                                <small class="text-muted">للأدوية التي تحتاج تبريد</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning text-dark me-2">
                                <i class="fas fa-shield-alt"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مستودع الأدوية المراقبة</h6>
                                <small class="text-muted">للأدوية الخاضعة للرقابة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Current Statistics (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المستودع
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary mb-1">{{ object.inventory_items.count }}</h4>
                        <small class="text-muted">عنصر مخزون</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">
                            {% if object.is_active %}
                            <i class="fas fa-check-circle"></i>
                            {% else %}
                            <i class="fas fa-times-circle text-danger"></i>
                            {% endif %}
                        </h4>
                        <small class="text-muted">الحالة</small>
                    </div>
                </div>
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        أنشئ في {{ object.created_at|date:"d/m/Y" }}
                    </small>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on the name field
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    if (nameField) {
        nameField.focus();
    }
    
    // Auto-generate code from name
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    if (nameField && codeField && !codeField.value) {
        nameField.addEventListener('input', function() {
            const name = this.value.trim();
            if (name) {
                // Generate code from first letters of words
                const words = name.split(' ');
                let code = '';
                words.forEach(word => {
                    if (word.length > 0) {
                        code += word.charAt(0).toUpperCase();
                    }
                });
                // Limit to 6 characters
                code = code.substring(0, 6);
                codeField.value = code;
            }
        });
    }
    
    // Phone number formatting
    const phoneField = document.getElementById('{{ form.phone.id_for_label }}');
    if (phoneField) {
        phoneField.addEventListener('input', function() {
            // Remove non-digits
            let value = this.value.replace(/\D/g, '');
            
            // Format as Saudi phone number if starts with 05
            if (value.startsWith('05') && value.length <= 10) {
                if (value.length > 3) {
                    value = value.substring(0, 3) + '-' + value.substring(3);
                }
                if (value.length > 7) {
                    value = value.substring(0, 7) + '-' + value.substring(7);
                }
            }
            
            this.value = value;
        });
    }
    
    // Form validation
    document.getElementById('warehouseForm').addEventListener('submit', function(e) {
        const name = document.getElementById('{{ form.name.id_for_label }}').value.trim();
        const code = document.getElementById('{{ form.code.id_for_label }}').value.trim();
        const location = document.getElementById('{{ form.location.id_for_label }}').value.trim();
        
        if (!name) {
            alert('يرجى إدخال اسم المستودع');
            e.preventDefault();
            return;
        }
        
        if (!code) {
            alert('يرجى إدخال رمز المستودع');
            e.preventDefault();
            return;
        }
        
        if (!location) {
            alert('يرجى إدخال موقع المستودع');
            e.preventDefault();
            return;
        }
        
        // Check if code contains only letters and numbers
        if (!/^[A-Za-z0-9]+$/.test(code)) {
            alert('رمز المستودع يجب أن يحتوي على أحرف وأرقام فقط');
            e.preventDefault();
            return;
        }
    });
});
</script>
{% endblock %}
