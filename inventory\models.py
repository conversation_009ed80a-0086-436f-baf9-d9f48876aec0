from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from drugs.models import Drug

class Warehouse(models.Model):
    """المستودعات"""
    name = models.Char<PERSON>ield(max_length=200, verbose_name="اسم المستودع")
    code = models.CharField(max_length=20, unique=True, verbose_name="رمز المستودع")
    location = models.CharField(max_length=200, verbose_name="الموقع")

    # معلومات المستودع
    manager = models.Char<PERSON>ield(max_length=200, verbose_name="مدير المستودع")
    phone = models.CharField(max_length=20, verbose_name="الهاتف")
    capacity = models.PositiveIntegerField(verbose_name="السعة التخزينية")

    # حالة المستودع
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_main_warehouse = models.BooleanField(default=False, verbose_name="مستودع رئيسي")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "المستودع"
        verbose_name_plural = "المستودعات"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

class DrugBatch(models.Model):
    """دفعات الأدوية"""
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, related_name='batches', verbose_name="الدواء")
    batch_number = models.CharField(max_length=100, verbose_name="رقم الدفعة")
    manufacturing_date = models.DateField(verbose_name="تاريخ التصنيع")
    expiry_date = models.DateField(verbose_name="تاريخ الانتهاء")

    # معلومات الدفعة
    supplier_batch_number = models.CharField(max_length=100, blank=True, verbose_name="رقم دفعة المورد")
    cost_price = models.DecimalField(max_digits=10, decimal_places=2,
                                   validators=[MinValueValidator(Decimal('0.01'))],
                                   verbose_name="سعر التكلفة")

    # حالة الدفعة
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_recalled = models.BooleanField(default=False, verbose_name="مسحوب")
    recall_reason = models.TextField(blank=True, verbose_name="سبب السحب")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "دفعة الدواء"
        verbose_name_plural = "دفعات الأدوية"
        ordering = ['expiry_date']
        unique_together = ['drug', 'batch_number']

    def __str__(self):
        return f"{self.drug.commercial_name} - {self.batch_number}"

    @property
    def is_expired(self):
        """فحص انتهاء الصلاحية"""
        from django.utils import timezone
        return self.expiry_date < timezone.now().date()

    @property
    def days_to_expiry(self):
        """عدد الأيام المتبقية للانتهاء"""
        from django.utils import timezone
        delta = self.expiry_date - timezone.now().date()
        return delta.days

class InventoryItem(models.Model):
    """عناصر المخزون"""
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, related_name='inventory_items', verbose_name="المستودع")
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, related_name='inventory_items', verbose_name="الدواء")
    batch = models.ForeignKey(DrugBatch, on_delete=models.CASCADE, related_name='inventory_items', verbose_name="الدفعة")

    # كميات المخزون
    quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية الحالية")
    reserved_quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية المحجوزة")
    minimum_stock = models.PositiveIntegerField(default=10, verbose_name="الحد الأدنى للمخزون")
    maximum_stock = models.PositiveIntegerField(default=1000, verbose_name="الحد الأقصى للمخزون")

    # معلومات الموقع
    shelf_location = models.CharField(max_length=50, blank=True, verbose_name="موقع الرف")

    # تواريخ
    last_updated = models.DateTimeField(auto_now=True, verbose_name="آخر تحديث")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "عنصر المخزون"
        verbose_name_plural = "عناصر المخزون"
        ordering = ['warehouse', 'drug']
        unique_together = ['warehouse', 'drug', 'batch']

    def __str__(self):
        return f"{self.warehouse.name} - {self.drug.commercial_name} - {self.batch.batch_number}"

    @property
    def available_quantity(self):
        """الكمية المتاحة (الكمية الحالية - المحجوزة)"""
        return self.quantity - self.reserved_quantity

    @property
    def is_low_stock(self):
        """فحص انخفاض المخزون"""
        return self.available_quantity <= self.minimum_stock

    @property
    def is_out_of_stock(self):
        """فحص نفاد المخزون"""
        return self.available_quantity <= 0

class InventoryMovement(models.Model):
    """حركات المخزون"""
    MOVEMENT_TYPES = [
        ('in', 'إدخال'),
        ('out', 'إخراج'),
        ('transfer', 'نقل'),
        ('adjustment', 'تسوية'),
        ('return', 'إرجاع'),
        ('damage', 'تلف'),
        ('expiry', 'انتهاء صلاحية'),
    ]

    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, related_name='movements', verbose_name="عنصر المخزون")
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES, verbose_name="نوع الحركة")
    quantity = models.IntegerField(verbose_name="الكمية")

    # معلومات الحركة
    reference_number = models.CharField(max_length=100, blank=True, verbose_name="رقم المرجع")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات النقل (في حالة النقل)
    from_warehouse = models.ForeignKey(Warehouse, on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='outgoing_movements', verbose_name="من مستودع")
    to_warehouse = models.ForeignKey(Warehouse, on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='incoming_movements', verbose_name="إلى مستودع")

    # معلومات المستخدم
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, verbose_name="أنشأ بواسطة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "حركة المخزون"
        verbose_name_plural = "حركات المخزون"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_movement_type_display()} - {self.inventory_item.drug.commercial_name} - {self.quantity}"

    def save(self, *args, **kwargs):
        """تحديث كمية المخزون عند حفظ الحركة"""
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # تحديث كمية المخزون حسب نوع الحركة
            if self.movement_type in ['in', 'return']:
                self.inventory_item.quantity += self.quantity
            elif self.movement_type in ['out', 'damage', 'expiry']:
                self.inventory_item.quantity -= self.quantity
            elif self.movement_type == 'adjustment':
                # في حالة التسوية، الكمية تكون الفرق
                self.inventory_item.quantity = self.quantity

            self.inventory_item.save()
