from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from drugs.models import DrugCategory, Manufacturer, Drug
from inventory.models import Warehouse, DrugBatch, InventoryItem, InventoryMovement
from procurement.models import Supplier, PurchaseOrder, PurchaseOrderItem
from accounts.models import Notification


class Command(BaseCommand):
    help = 'Add simple demo data to the system'

    def handle(self, *args, **options):
        self.stdout.write('Adding simple demo data...')
        
        self.create_suppliers()
        self.create_purchase_orders()
        self.create_movements()
        self.create_notifications()

        self.stdout.write(
            self.style.SUCCESS('Successfully added simple demo data!')
        )

    def create_suppliers(self):
        """Create suppliers"""
        self.stdout.write('Creating suppliers...')
        
        suppliers = [
            {
                'name': 'شركة الدواء السعودية للتوريد',
                'contact_person': 'أحمد محمد العلي',
                'email': '<EMAIL>',
                'phone': '+************',
                'address': 'الرياض، المملكة العربية السعودية',
                'supplier_type': 'local',
                'rating': 4.8,
                'is_active': True,
            },
            {
                'name': 'شركة فايزر الشرق الأوسط',
                'contact_person': 'سارة أحمد',
                'email': '<EMAIL>',
                'phone': '+966112345679',
                'address': 'دبي، الإمارات العربية المتحدة',
                'supplier_type': 'international',
                'rating': 4.5,
                'is_active': True,
            },
            {
                'name': 'مؤسسة الجزيرة الطبية',
                'contact_person': 'محمد سالم',
                'email': '<EMAIL>',
                'phone': '+966112345680',
                'address': 'جدة، المملكة العربية السعودية',
                'supplier_type': 'local',
                'rating': 4.2,
                'is_active': True,
            },
        ]

        for supplier_data in suppliers:
            Supplier.objects.get_or_create(
                name=supplier_data['name'],
                defaults=supplier_data
            )

    def create_purchase_orders(self):
        """Create purchase orders"""
        self.stdout.write('Creating purchase orders...')
        
        users = User.objects.all()
        drugs = Drug.objects.all()
        suppliers = Supplier.objects.all()
        statuses = ['draft', 'sent', 'confirmed', 'received']
        
        for i in range(10):  # Create 10 purchase orders
            po_number = f"PO-2024-{str(i+1).zfill(3)}"
            
            # Random date within last 2 months
            po_date = timezone.now() - timedelta(
                days=random.randint(0, 60)
            )
            
            # Expected delivery date (1-3 weeks from order date)
            expected_delivery = po_date + timedelta(
                days=random.randint(7, 21)
            )
            
            po = PurchaseOrder.objects.create(
                po_number=po_number,
                supplier=random.choice(suppliers),
                created_by=random.choice(users),
                status=random.choice(statuses),
                expected_delivery_date=expected_delivery.date(),
                notes=f'أمر شراء رقم {po_number}',
                created_at=po_date,
            )
            
            # Add purchase order items (3-6 items per order)
            po_drugs = random.sample(list(drugs), random.randint(3, 6))
            for drug in po_drugs:
                unit_cost = Decimal(str(random.uniform(5.0, 80.0)))
                quantity = random.randint(50, 300)
                
                PurchaseOrderItem.objects.create(
                    purchase_order=po,
                    drug=drug,
                    quantity=quantity,
                    unit_cost=unit_cost,
                    total_cost=unit_cost * quantity,
                    notes=f'صنف {drug.commercial_name} في أمر الشراء {po_number}',
                )

    def create_movements(self):
        """Create inventory movements"""
        self.stdout.write('Creating inventory movements...')
        
        inventory_items = InventoryItem.objects.all()
        users = User.objects.all()
        movement_types = ['in', 'out', 'adjustment']
        
        for _ in range(50):  # Create 50 movements
            item = random.choice(inventory_items)
            movement_type = random.choice(movement_types)
            
            if movement_type == 'in':
                quantity = random.randint(10, 100)
                reason = 'استلام من المورد'
            elif movement_type == 'out':
                quantity = random.randint(1, min(20, item.quantity))
                reason = 'صرف للمريض'
            else:  # adjustment
                quantity = random.randint(-10, 10)
                reason = 'تسوية جرد'
            
            # Random date within last month
            movement_date = timezone.now() - timedelta(
                days=random.randint(0, 30)
            )
            
            InventoryMovement.objects.create(
                inventory_item=item,
                movement_type=movement_type,
                quantity=quantity,
                reason=reason,
                created_by=random.choice(users),
                created_at=movement_date,
                notes=f'حركة {movement_type} لـ {item.drug.commercial_name}',
            )

    def create_notifications(self):
        """Create notifications"""
        self.stdout.write('Creating notifications...')
        
        users = User.objects.all()
        
        notifications_data = [
            {'title': 'مخزون منخفض', 'message': 'يوجد 5 أصناف دواء بمخزون منخفض', 'type': 'warning'},
            {'title': 'طلب جديد', 'message': 'تم استلام طلب جديد من مستشفى الملك فهد', 'type': 'info'},
            {'title': 'انتهاء صلاحية', 'message': 'يوجد 3 أصناف ستنتهي صلاحيتها خلال أسبوع', 'type': 'error'},
            {'title': 'تم الموافقة', 'message': 'تم الموافقة على أمر الشراء PO-2024-001', 'type': 'success'},
            {'title': 'استلام شحنة', 'message': 'تم استلام شحنة جديدة من شركة الدواء السعودية', 'type': 'success'},
            {'title': 'طلب عاجل', 'message': 'طلب عاجل من قسم الطوارئ يحتاج موافقة فورية', 'type': 'warning'},
        ]
        
        for user in users:
            # Create 2-4 notifications per user
            user_notifications = random.sample(notifications_data, random.randint(2, 4))
            for notif_data in user_notifications:
                # Random date within last 2 weeks
                notif_date = timezone.now() - timedelta(
                    days=random.randint(0, 14)
                )
                
                Notification.objects.create(
                    user=user,
                    title=notif_data['title'],
                    message=notif_data['message'],
                    notification_type=notif_data['type'],
                    is_read=random.choice([True, False]),
                    created_at=notif_date,
                )
