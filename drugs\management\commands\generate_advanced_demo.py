from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from drugs.models import DrugCategory, Manufacturer, Drug
from inventory.models import Warehouse, Batch, InventoryItem, InventoryMovement
from orders.models import Order, OrderItem, InternalRequest, InternalRequestItem
from procurement.models import Supplier, PurchaseOrder, PurchaseOrderItem
from accounts.models import UserProfile, Notification


class Command(BaseCommand):
    help = 'Generate advanced demo data with realistic scenarios'

    def handle(self, *args, **options):
        self.stdout.write('Generating advanced demo data...')
        
        # Create realistic scenarios
        self.create_low_stock_scenario()
        self.create_expiry_scenario()
        self.create_urgent_orders_scenario()
        self.create_supplier_performance_data()
        self.create_seasonal_trends()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully generated advanced demo data!')
        )

    def create_low_stock_scenario(self):
        """Create realistic low stock scenarios"""
        self.stdout.write('Creating low stock scenarios...')
        
        # Get some random drugs and set them to low stock
        drugs = Drug.objects.all()
        low_stock_drugs = random.sample(list(drugs), min(15, len(drugs)))
        
        for drug in low_stock_drugs:
            inventory_items = InventoryItem.objects.filter(drug=drug)
            for item in inventory_items:
                # Set quantity below minimum
                item.quantity = random.randint(0, item.min_quantity - 1)
                item.save()
                
                # Create movement record for the low stock
                InventoryMovement.objects.create(
                    inventory_item=item,
                    movement_type='out',
                    quantity=-random.randint(10, 30),
                    reason='استهلاك عالي - مخزون منخفض',
                    created_by=User.objects.first(),
                    notes=f'انخفاض مخزون {drug.name} إلى مستوى حرج',
                )

    def create_expiry_scenario(self):
        """Create realistic expiry scenarios"""
        self.stdout.write('Creating expiry scenarios...')
        
        # Create some batches that are expired or expiring soon
        drugs = Drug.objects.all()
        
        for drug in random.sample(list(drugs), min(10, len(drugs))):
            # Create expired batch
            expired_batch = Batch.objects.create(
                batch_number=f"EXP-{drug.name[:3].upper()}-{random.randint(100, 999)}",
                drug=drug,
                manufacturing_date=timezone.now().date() - timedelta(days=random.randint(400, 800)),
                expiry_date=timezone.now().date() - timedelta(days=random.randint(1, 30)),
                supplier=Supplier.objects.order_by('?').first(),
                cost_per_unit=Decimal(str(random.uniform(1.0, 50.0))),
                notes=f'دفعة منتهية الصلاحية من {drug.name}',
            )
            
            # Create expiring soon batch
            expiring_batch = Batch.objects.create(
                batch_number=f"EXS-{drug.name[:3].upper()}-{random.randint(100, 999)}",
                drug=drug,
                manufacturing_date=timezone.now().date() - timedelta(days=random.randint(200, 400)),
                expiry_date=timezone.now().date() + timedelta(days=random.randint(1, 30)),
                supplier=Supplier.objects.order_by('?').first(),
                cost_per_unit=Decimal(str(random.uniform(1.0, 50.0))),
                notes=f'دفعة تنتهي قريباً من {drug.name}',
            )

    def create_urgent_orders_scenario(self):
        """Create urgent orders that need immediate attention"""
        self.stdout.write('Creating urgent orders scenarios...')
        
        users = User.objects.all()
        drugs = Drug.objects.all()
        
        # Create urgent orders
        for i in range(5):
            order_number = f"URG-2024-{str(i+1).zfill(3)}"
            
            order = Order.objects.create(
                order_number=order_number,
                institution='مستشفى الملك فهد - قسم الطوارئ',
                department='الطوارئ',
                requested_by=random.choice(users),
                status='pending',
                priority='urgent',
                notes=f'طلب عاجل - حالة طوارئ - {order_number}',
                created_at=timezone.now() - timedelta(hours=random.randint(1, 12)),
            )
            
            # Add critical drugs to urgent orders
            critical_drugs = random.sample(list(drugs), random.randint(3, 6))
            for drug in critical_drugs:
                OrderItem.objects.create(
                    order=order,
                    drug=drug,
                    quantity_requested=random.randint(5, 25),
                    quantity_approved=0,
                    unit_cost=Decimal(str(random.uniform(5.0, 100.0))),
                    notes=f'صنف عاجل - {drug.name}',
                )

    def create_supplier_performance_data(self):
        """Create realistic supplier performance data"""
        self.stdout.write('Creating supplier performance data...')
        
        suppliers = Supplier.objects.all()
        
        for supplier in suppliers:
            # Create purchase orders with different performance metrics
            for i in range(random.randint(3, 8)):
                po_number = f"PERF-{supplier.name[:3].upper()}-{random.randint(100, 999)}"
                
                # Random delivery performance
                order_date = timezone.now() - timedelta(days=random.randint(30, 180))
                expected_delivery = order_date + timedelta(days=random.randint(7, 21))
                
                # Some orders are late, some on time, some early
                performance_factor = random.choice([-3, -2, -1, 0, 1, 2, 3, 4, 5])
                actual_delivery = expected_delivery + timedelta(days=performance_factor)
                
                status = 'received' if actual_delivery <= timezone.now() else 'confirmed'
                
                po = PurchaseOrder.objects.create(
                    po_number=po_number,
                    supplier=supplier,
                    created_by=User.objects.order_by('?').first(),
                    status=status,
                    expected_delivery_date=expected_delivery.date(),
                    actual_delivery_date=actual_delivery.date() if status == 'received' else None,
                    notes=f'أمر شراء لتقييم أداء {supplier.name}',
                    created_at=order_date,
                )
                
                # Add items to purchase order
                drugs = random.sample(list(Drug.objects.all()), random.randint(2, 5))
                for drug in drugs:
                    unit_cost = Decimal(str(random.uniform(1.0, 80.0)))
                    quantity = random.randint(20, 200)
                    
                    PurchaseOrderItem.objects.create(
                        purchase_order=po,
                        drug=drug,
                        quantity=quantity,
                        unit_cost=unit_cost,
                        total_cost=unit_cost * quantity,
                        notes=f'صنف {drug.name} - تقييم أداء',
                    )

    def create_seasonal_trends(self):
        """Create seasonal usage trends"""
        self.stdout.write('Creating seasonal trends...')
        
        # Create more movements for certain drugs in winter (respiratory drugs)
        respiratory_drugs = Drug.objects.filter(
            category__name__icontains='الجهاز التنفسي'
        )
        
        for drug in respiratory_drugs:
            inventory_items = InventoryItem.objects.filter(drug=drug)
            for item in inventory_items:
                # Create more outbound movements (higher usage in winter)
                for _ in range(random.randint(5, 15)):
                    movement_date = timezone.now() - timedelta(
                        days=random.randint(0, 90)
                    )
                    
                    InventoryMovement.objects.create(
                        inventory_item=item,
                        movement_type='out',
                        quantity=-random.randint(1, 10),
                        reason='استهلاك موسمي - فصل الشتاء',
                        created_by=User.objects.order_by('?').first(),
                        created_at=movement_date,
                        notes=f'زيادة استهلاك {drug.name} في الشتاء',
                    )
        
        # Create trends for diabetes drugs (consistent high usage)
        diabetes_drugs = Drug.objects.filter(
            category__name__icontains='السكري'
        )
        
        for drug in diabetes_drugs:
            inventory_items = InventoryItem.objects.filter(drug=drug)
            for item in inventory_items:
                # Create consistent movements
                for _ in range(random.randint(10, 20)):
                    movement_date = timezone.now() - timedelta(
                        days=random.randint(0, 180)
                    )
                    
                    InventoryMovement.objects.create(
                        inventory_item=item,
                        movement_type='out',
                        quantity=-random.randint(2, 8),
                        reason='استهلاك منتظم - مرضى مزمنين',
                        created_by=User.objects.order_by('?').first(),
                        created_at=movement_date,
                        notes=f'استهلاك منتظم لـ {drug.name}',
                    )

    def create_realistic_notifications(self):
        """Create realistic notifications based on current data"""
        self.stdout.write('Creating realistic notifications...')
        
        users = User.objects.all()
        
        # Low stock notifications
        low_stock_items = InventoryItem.objects.filter(
            quantity__lt=models.F('min_quantity')
        )
        
        for user in users:
            if low_stock_items.exists():
                Notification.objects.create(
                    user=user,
                    title='تحذير: مخزون منخفض',
                    message=f'يوجد {low_stock_items.count()} صنف بمخزون أقل من الحد الأدنى',
                    notification_type='warning',
                    is_read=False,
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 24)),
                )
        
        # Expiry notifications
        expiring_batches = Batch.objects.filter(
            expiry_date__lte=timezone.now().date() + timedelta(days=30)
        )
        
        for user in users:
            if expiring_batches.exists():
                Notification.objects.create(
                    user=user,
                    title='تحذير: انتهاء صلاحية',
                    message=f'يوجد {expiring_batches.count()} دفعة ستنتهي صلاحيتها خلال 30 يوم',
                    notification_type='error',
                    is_read=False,
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 48)),
                )
        
        # Pending orders notifications
        pending_orders = Order.objects.filter(status='pending')
        
        for user in users:
            if pending_orders.exists():
                Notification.objects.create(
                    user=user,
                    title='طلبات في الانتظار',
                    message=f'يوجد {pending_orders.count()} طلب في انتظار المراجعة',
                    notification_type='info',
                    is_read=random.choice([True, False]),
                    created_at=timezone.now() - timedelta(hours=random.randint(1, 72)),
                )
