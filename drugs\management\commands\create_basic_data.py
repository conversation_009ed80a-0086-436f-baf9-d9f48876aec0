from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from drugs.models import DrugCategory, Manufacturer, Drug
from inventory.models import Warehouse, DrugBatch, InventoryItem
from accounts.models import UserProfile


class Command(BaseCommand):
    help = 'Create basic demo data for the Drug Management System'

    def handle(self, *args, **options):
        self.stdout.write('Creating basic demo data...')

        # Create basic data
        self.create_users()
        self.create_categories()
        self.create_manufacturers()
        self.create_drugs()
        self.create_warehouses()
        self.create_batches()
        self.create_inventory()

        self.stdout.write(
            self.style.SUCCESS('Successfully created basic demo data!')
        )

    def create_users(self):
        """Create demo users"""
        self.stdout.write('Creating users...')

        # Create admin user if not exists
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'first_name': 'مدير',
                'last_name': 'النظام',
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()

        # Create other users
        users_data = [
            {'username': 'pharmacist1', 'first_name': 'أحمد', 'last_name': 'محمد'},
            {'username': 'pharmacist2', 'first_name': 'فاطمة', 'last_name': 'علي'},
            {'username': 'manager1', 'first_name': 'محمد', 'last_name': 'سالم'},
        ]

        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'email': f"{user_data['username']}@hospital.com",
                    'is_staff': True,
                }
            )
            if created:
                user.set_password('password123')
                user.save()

    def create_categories(self):
        """Create drug categories"""
        self.stdout.write('Creating categories...')

        categories = [
            'مضادات حيوية',
            'مسكنات',
            'أدوية القلب',
            'أدوية السكري',
            'أدوية الضغط',
            'فيتامينات ومكملات',
        ]

        for cat_name in categories:
            DrugCategory.objects.get_or_create(
                name=cat_name,
                defaults={'description': f'تصنيف {cat_name}'}
            )

    def create_manufacturers(self):
        """Create manufacturers"""
        self.stdout.write('Creating manufacturers...')

        manufacturers = [
            {'name': 'شركة الدواء السعودية', 'country': 'السعودية'},
            {'name': 'شركة فايزر', 'country': 'أمريكا'},
            {'name': 'شركة نوفارتيس', 'country': 'سويسرا'},
            {'name': 'شركة الجزيرة للأدوية', 'country': 'السعودية'},
        ]

        for man_data in manufacturers:
            Manufacturer.objects.get_or_create(
                name=man_data['name'],
                defaults={
                    'country': man_data['country'],
                    'contact_info': f'معلومات الاتصال لـ {man_data["name"]}',
                }
            )

    def create_drugs(self):
        """Create drugs"""
        self.stdout.write('Creating drugs...')

        drugs_data = [
            {'name': 'بانادول 500 مجم', 'category': 'مسكنات', 'manufacturer': 'شركة فايزر'},
            {'name': 'أموكسيسيلين 500 مجم', 'category': 'مضادات حيوية', 'manufacturer': 'شركة الدواء السعودية'},
            {'name': 'جلوكوفاج 850 مجم', 'category': 'أدوية السكري', 'manufacturer': 'شركة نوفارتيس'},
            {'name': 'نورفاسك 5 مجم', 'category': 'أدوية الضغط', 'manufacturer': 'شركة فايزر'},
            {'name': 'أتينولول 50 مجم', 'category': 'أدوية القلب', 'manufacturer': 'شركة الجزيرة للأدوية'},
            {'name': 'فيتامين د 1000 وحدة', 'category': 'فيتامينات ومكملات', 'manufacturer': 'شركة الدواء السعودية'},
            {'name': 'إيبوبروفين 400 مجم', 'category': 'مسكنات', 'manufacturer': 'شركة نوفارتيس'},
            {'name': 'أزيثروميسين 250 مجم', 'category': 'مضادات حيوية', 'manufacturer': 'شركة فايزر'},
        ]

        categories = {cat.name: cat for cat in DrugCategory.objects.all()}
        manufacturers = {man.name: man for man in Manufacturer.objects.all()}

        for drug_data in drugs_data:
            # Extract strength
            strength = ''
            if 'مجم' in drug_data['name']:
                parts = drug_data['name'].split()
                for i, part in enumerate(parts):
                    if 'مجم' in part and i > 0:
                        strength = f"{parts[i-1]} {part}"
                        break
            elif 'وحدة' in drug_data['name']:
                parts = drug_data['name'].split()
                for i, part in enumerate(parts):
                    if 'وحدة' in part and i > 0:
                        strength = f"{parts[i-1]} {part}"
                        break

            Drug.objects.get_or_create(
                scientific_name=drug_data['name'],
                defaults={
                    'commercial_name': drug_data['name'],
                    'category': categories[drug_data['category']],
                    'manufacturer': manufacturers[drug_data['manufacturer']],
                    'unit': 'tablet',
                    'unit_price': Decimal(str(random.uniform(5.0, 50.0))),
                    'strength': strength,
                    'dosage_form': 'أقراص',
                    'description': f'وصف {drug_data["name"]}',
                    'requires_prescription': True,
                }
            )

    def create_warehouses(self):
        """Create warehouses"""
        self.stdout.write('Creating warehouses...')

        warehouses = [
            {
                'name': 'المستودع الرئيسي',
                'code': 'MAIN-001',
                'location': 'الدور الأرضي',
                'manager': 'أحمد محمد',
                'phone': '0501234567',
                'capacity': 10000,
                'is_main_warehouse': True,
            },
            {
                'name': 'مستودع الطوارئ',
                'code': 'EMER-001',
                'location': 'قسم الطوارئ',
                'manager': 'فاطمة علي',
                'phone': '0501234568',
                'capacity': 2000,
                'is_main_warehouse': False,
            },
        ]

        for warehouse_data in warehouses:
            Warehouse.objects.get_or_create(
                name=warehouse_data['name'],
                defaults=warehouse_data
            )

    def create_batches(self):
        """Create drug batches"""
        self.stdout.write('Creating drug batches...')

        drugs = Drug.objects.all()

        for drug in drugs:
            # Create 1-2 batches per drug
            for i in range(random.randint(1, 2)):
                batch_number = f"{drug.commercial_name[:3].upper()}-2024-{str(i+1).zfill(3)}"

                # Random expiry date (6 months to 2 years from now)
                expiry_date = timezone.now().date() + timedelta(
                    days=random.randint(180, 730)
                )

                # Random manufacturing date (1-6 months ago)
                manufacturing_date = timezone.now().date() - timedelta(
                    days=random.randint(30, 180)
                )

                DrugBatch.objects.get_or_create(
                    batch_number=batch_number,
                    defaults={
                        'drug': drug,
                        'manufacturing_date': manufacturing_date,
                        'expiry_date': expiry_date,
                        'cost_price': Decimal(str(random.uniform(1.0, 50.0))),
                    }
                )

    def create_inventory(self):
        """Create basic inventory items"""
        self.stdout.write('Creating inventory items...')

        drugs = Drug.objects.all()
        warehouses = Warehouse.objects.all()

        for drug in drugs:
            batches = DrugBatch.objects.filter(drug=drug)
            for warehouse in warehouses:
                for batch in batches:
                    quantity = random.randint(50, 500)
                    min_quantity = random.randint(10, 30)
                    max_quantity = random.randint(200, 1000)

                    InventoryItem.objects.get_or_create(
                        drug=drug,
                        warehouse=warehouse,
                        batch=batch,
                        defaults={
                            'quantity': quantity,
                            'minimum_stock': min_quantity,
                            'maximum_stock': max_quantity,
                            'shelf_location': f'رف {random.randint(1, 10)}-{random.randint(1, 5)}',
                        }
                    )
