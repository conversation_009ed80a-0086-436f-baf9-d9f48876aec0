{% extends 'base/base.html' %}
{% load static %}

{% block title %}المستودعات - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item active">المستودعات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-building me-2"></i>
                المستودعات
            </h1>
            <a href="{% url 'inventory:warehouse_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة مستودع جديد
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة المستودعات ({{ warehouses|length }} مستودع)
                </h5>
            </div>
            <div class="card-body">
                {% if warehouses %}
                <div class="row">
                    {% for warehouse in warehouses %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 {% if warehouse.is_main_warehouse %}border-primary{% else %}border-secondary{% endif %}">
                            <div class="card-header {% if warehouse.is_main_warehouse %}bg-primary text-white{% endif %}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="card-title mb-0">
                                        <i class="fas fa-building me-2"></i>
                                        {{ warehouse.name }}
                                    </h6>
                                    {% if warehouse.is_main_warehouse %}
                                    <span class="badge bg-warning text-dark">رئيسي</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">الرمز:</small>
                                    <span class="fw-bold">{{ warehouse.code }}</span>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">الموقع:</small>
                                    <p class="mb-0">{{ warehouse.location }}</p>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">المدير:</small>
                                    <p class="mb-0">{{ warehouse.manager }}</p>
                                </div>

                                <div class="mb-3">
                                    <small class="text-muted">الهاتف:</small>
                                    <p class="mb-0">{{ warehouse.phone }}</p>
                                </div>

                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <h5 class="text-primary mb-1">{{ warehouse.inventory_items.count }}</h5>
                                        <small class="text-muted">عنصر مخزون</small>
                                    </div>
                                    <div class="col-6">
                                        <h5 class="text-info mb-1">{{ warehouse.capacity }}</h5>
                                        <small class="text-muted">السعة</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    {% if warehouse.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'inventory:warehouse_detail' warehouse.pk %}"
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:warehouse_edit' warehouse.pk %}"
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'inventory:inventory_list' %}?warehouse={{ warehouse.id }}"
                                       class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-warehouse"></i> المخزون
                                    </a>
                                    <a href="{% url 'inventory:warehouse_delete' warehouse.pk %}"
                                       class="btn btn-outline-danger btn-sm"
                                       onclick="return confirm('هل أنت متأكد من حذف هذا المستودع؟')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستودعات</h5>
                    <p class="text-muted">لم يتم إضافة أي مستودعات بعد.</p>
                    <a href="{% url 'inventory:warehouse_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول مستودع
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
{% if warehouses %}
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-primary">{{ warehouses|length }}</h4>
                <small class="text-muted">إجمالي المستودعات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-success">
                    {% with active_count=0 %}
                        {% for warehouse in warehouses %}
                            {% if warehouse.is_active %}
                                {% with active_count=active_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ active_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مستودعات نشطة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-warning">
                    {% with main_count=0 %}
                        {% for warehouse in warehouses %}
                            {% if warehouse.is_main_warehouse %}
                                {% with main_count=main_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ main_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مستودعات رئيسية</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="text-info">
                    {% with total_capacity=0 %}
                        {% for warehouse in warehouses %}
                            {% with total_capacity=total_capacity|add:warehouse.capacity %}{% endwith %}
                        {% endfor %}
                        {{ total_capacity }}
                    {% endwith %}
                </h4>
                <small class="text-muted">إجمالي السعة</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
