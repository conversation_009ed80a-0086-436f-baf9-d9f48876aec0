{% extends 'base/base.html' %}
{% load static %}

{% block title %}قائمة الأدوية - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">قائمة الأدوية</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-pills me-2"></i>
                قائمة الأدوية
            </h1>
            <a href="{% url 'drugs:drug_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة دواء جديد
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search }}" placeholder="البحث في الأدوية...">
                    </div>
                    <div class="col-md-3">
                        <label for="category" class="form-label">التصنيف</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع التصنيفات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="manufacturer" class="form-label">الشركة المصنعة</label>
                        <select class="form-select" id="manufacturer" name="manufacturer">
                            <option value="">جميع الشركات</option>
                            {% for manufacturer in manufacturers %}
                            <option value="{{ manufacturer.id }}" {% if selected_manufacturer == manufacturer.id|stringformat:"s" %}selected{% endif %}>
                                {{ manufacturer.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Drugs Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    الأدوية المتاحة ({{ drugs|length }} دواء)
                </h5>
            </div>
            <div class="card-body">
                {% if drugs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم التجاري</th>
                                <th>الاسم العلمي</th>
                                <th>التصنيف</th>
                                <th>الشركة المصنعة</th>
                                <th>التركيز</th>
                                <th>الوحدة</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for drug in drugs %}
                            <tr>
                                <td>
                                    <strong>{{ drug.commercial_name }}</strong>
                                    {% if drug.is_controlled %}
                                    <span class="badge bg-warning text-dark ms-2">مراقب</span>
                                    {% endif %}
                                </td>
                                <td>{{ drug.scientific_name }}</td>
                                <td>{{ drug.category.name }}</td>
                                <td>{{ drug.manufacturer.name }}</td>
                                <td>{{ drug.strength|default:"-" }}</td>
                                <td>{{ drug.get_unit_display }}</td>
                                <td>{{ drug.unit_price }} ريال</td>
                                <td>
                                    {% if drug.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'drugs:drug_detail' drug.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'drugs:drug_edit' drug.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'drugs:drug_delete' drug.pk %}" 
                                           class="btn btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_manufacturer %}&manufacturer={{ selected_manufacturer }}{% endif %}">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_manufacturer %}&manufacturer={{ selected_manufacturer }}{% endif %}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_manufacturer %}&manufacturer={{ selected_manufacturer }}{% endif %}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_manufacturer %}&manufacturer={{ selected_manufacturer }}{% endif %}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية</h5>
                    <p class="text-muted">لم يتم العثور على أي أدوية تطابق معايير البحث.</p>
                    <a href="{% url 'drugs:drug_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دواء
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on filter change
document.getElementById('category').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('manufacturer').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
