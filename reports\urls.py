from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    # Dashboard and Overview
    path('', views.ReportsDashboardView.as_view(), name='reports_dashboard'),
    
    # Inventory Reports
    path('inventory/', views.InventoryReportView.as_view(), name='inventory_report'),
    path('inventory/low-stock/', views.LowStockReportView.as_view(), name='low_stock_report'),
    path('inventory/expiry/', views.ExpiryReportView.as_view(), name='expiry_report'),
    path('inventory/movements/', views.InventoryMovementReportView.as_view(), name='inventory_movement_report'),
    
    # Order Reports
    path('orders/', views.OrderReportView.as_view(), name='order_report'),
    path('orders/pending/', views.PendingOrdersReportView.as_view(), name='pending_orders_report'),
    path('orders/fulfilled/', views.FulfilledOrdersReportView.as_view(), name='fulfilled_orders_report'),
    path('orders/by-institution/', views.OrdersByInstitutionReportView.as_view(), name='orders_by_institution_report'),
    
    # Usage Reports
    path('usage/', views.UsageReportView.as_view(), name='usage_report'),
    path('usage/by-drug/', views.UsageByDrugReportView.as_view(), name='usage_by_drug_report'),
    path('usage/by-department/', views.UsageByDepartmentReportView.as_view(), name='usage_by_department_report'),
    path('usage/trends/', views.UsageTrendsReportView.as_view(), name='usage_trends_report'),
    
    # Financial Reports
    path('financial/', views.FinancialReportView.as_view(), name='financial_report'),
    path('financial/cost-analysis/', views.CostAnalysisReportView.as_view(), name='cost_analysis_report'),
    path('financial/supplier-spending/', views.SupplierSpendingReportView.as_view(), name='supplier_spending_report'),
    
    # Supplier Reports
    path('suppliers/', views.SupplierReportView.as_view(), name='supplier_report'),
    path('suppliers/performance/', views.SupplierPerformanceReportView.as_view(), name='supplier_performance_report'),
    path('suppliers/delivery/', views.SupplierDeliveryReportView.as_view(), name='supplier_delivery_report'),
    
    # Custom Reports
    path('custom/', views.CustomReportView.as_view(), name='custom_report'),
    path('templates/', views.ReportTemplateListView.as_view(), name='report_template_list'),
    path('templates/create/', views.ReportTemplateCreateView.as_view(), name='report_template_create'),
    path('templates/<int:pk>/', views.ReportTemplateDetailView.as_view(), name='report_template_detail'),
    path('templates/<int:pk>/edit/', views.ReportTemplateUpdateView.as_view(), name='report_template_edit'),
    path('templates/<int:pk>/delete/', views.ReportTemplateDeleteView.as_view(), name='report_template_delete'),
    path('templates/<int:pk>/generate/', views.generate_report_from_template, name='generate_report_from_template'),
    
    # Generated Reports
    path('generated/', views.GeneratedReportListView.as_view(), name='generated_report_list'),
    path('generated/<int:pk>/', views.GeneratedReportDetailView.as_view(), name='generated_report_detail'),
    path('generated/<int:pk>/download/', views.download_generated_report, name='download_generated_report'),
    path('generated/<int:pk>/delete/', views.GeneratedReportDeleteView.as_view(), name='generated_report_delete'),
    
    # Export URLs
    path('export/excel/', views.export_to_excel, name='export_to_excel'),
    path('export/pdf/', views.export_to_pdf, name='export_to_pdf'),
    path('export/csv/', views.export_to_csv, name='export_to_csv'),
    
    # API URLs
    path('api/chart-data/', views.chart_data_api, name='chart_data_api'),
    path('api/report-preview/', views.report_preview_api, name='report_preview_api'),
]
