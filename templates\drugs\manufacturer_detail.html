{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ manufacturer.name }} - تفاصيل الشركة المصنعة{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:manufacturer_list' %}">الشركات المصنعة</a></li>
        <li class="breadcrumb-item active">{{ manufacturer.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-industry me-2"></i>
                {{ manufacturer.name }}
                {% if not manufacturer.is_active %}
                <span class="badge bg-secondary ms-2">غير نشط</span>
                {% endif %}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'drugs:manufacturer_edit' manufacturer.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <a href="{% url 'drugs:manufacturer_delete' manufacturer.pk %}" class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من حذف هذه الشركة؟')">
                    <i class="fas fa-trash me-2"></i>
                    حذف
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Manufacturer Information -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الشركة
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-industry"></i>
                    </div>
                    <h4 class="text-primary">{{ manufacturer.name }}</h4>
                    <span class="badge bg-info">{{ manufacturer.country }}</span>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted small">البلد</label>
                    <p class="fw-bold">{{ manufacturer.country }}</p>
                </div>
                
                {% if manufacturer.contact_info %}
                <div class="mb-3">
                    <label class="form-label text-muted small">معلومات الاتصال</label>
                    <p class="mb-0">{{ manufacturer.contact_info|linebreaks }}</p>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label text-muted small">الحالة</label>
                    <p>
                        {% if manufacturer.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </p>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary mb-1">{{ manufacturer.drug_set.count }}</h4>
                        <small class="text-muted">دواء</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ manufacturer.drug_set.filter.is_active.count|default:manufacturer.drug_set.count }}</h4>
                        <small class="text-muted">نشط</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <div class="d-flex justify-content-between mb-2">
                        <span>تاريخ الإضافة:</span>
                        <span>{{ manufacturer.created_at|date:"d/m/Y" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>آخر تحديث:</span>
                        <span>{{ manufacturer.updated_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>رقم الشركة:</span>
                        <span><code>#{{ manufacturer.id }}</code></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'drugs:drug_create' %}?manufacturer={{ manufacturer.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دواء لهذه الشركة
                    </a>
                    <a href="{% url 'drugs:drug_list' %}?manufacturer={{ manufacturer.id }}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع الأدوية
                    </a>
                    <a href="{% url 'drugs:manufacturer_edit' manufacturer.pk %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الشركة
                    </a>
                    <a href="#" class="btn btn-outline-success">
                        <i class="fas fa-chart-line me-2"></i>
                        تقرير الأداء
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Drugs by this Manufacturer -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pills me-2"></i>
                    الأدوية المصنعة ({{ manufacturer.drug_set.count }})
                </h5>
                {% if manufacturer.drug_set.exists %}
                <a href="{% url 'drugs:drug_list' %}?manufacturer={{ manufacturer.id }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if manufacturer.drug_set.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم التجاري</th>
                                <th>الاسم العلمي</th>
                                <th>التصنيف</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for drug in manufacturer.drug_set.all|slice:":10" %}
                            <tr>
                                <td>
                                    <strong>{{ drug.commercial_name }}</strong>
                                    {% if drug.is_controlled %}
                                    <span class="badge bg-warning text-dark ms-1">مراقب</span>
                                    {% endif %}
                                </td>
                                <td>{{ drug.scientific_name }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ drug.category.name }}</span>
                                </td>
                                <td class="text-success fw-bold">{{ drug.unit_price }} ريال</td>
                                <td>
                                    {% if drug.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'drugs:drug_detail' drug.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'drugs:drug_edit' drug.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if manufacturer.drug_set.count > 10 %}
                <div class="text-center mt-3">
                    <a href="{% url 'drugs:drug_list' %}?manufacturer={{ manufacturer.id }}" class="btn btn-primary">
                        عرض جميع الأدوية ({{ manufacturer.drug_set.count }})
                    </a>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية لهذه الشركة</h5>
                    <p class="text-muted">لم يتم إضافة أي أدوية لهذه الشركة المصنعة بعد.</p>
                    <a href="{% url 'drugs:drug_create' %}?manufacturer={{ manufacturer.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دواء
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Statistics and Analytics -->
        {% if manufacturer.drug_set.exists %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات وتحليلات
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-primary mb-1">{{ manufacturer.drug_set.count }}</h5>
                            <small class="text-muted">إجمالي الأدوية</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-success mb-1">{{ manufacturer.drug_set.filter.is_active.count|default:manufacturer.drug_set.count }}</h5>
                            <small class="text-muted">أدوية نشطة</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-warning mb-1">{{ manufacturer.drug_set.filter.is_controlled.count|default:0 }}</h5>
                            <small class="text-muted">أدوية مراقبة</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-info mb-1">
                                {% with categories=manufacturer.drug_set.values_list.category.distinct %}
                                {{ categories|length }}
                                {% endwith %}
                            </h5>
                            <small class="text-muted">تصنيف</small>
                        </div>
                    </div>
                </div>
                
                <!-- Price Analysis -->
                <div class="mt-3">
                    <h6 class="text-muted">تحليل الأسعار:</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <small class="text-muted">أقل سعر:</small>
                            <div class="fw-bold text-success">
                                {% with min_price=manufacturer.drug_set.aggregate.unit_price__min %}
                                {{ min_price|default:"غير محدد" }} ريال
                                {% endwith %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">أعلى سعر:</small>
                            <div class="fw-bold text-danger">
                                {% with max_price=manufacturer.drug_set.aggregate.unit_price__max %}
                                {{ max_price|default:"غير محدد" }} ريال
                                {% endwith %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">متوسط السعر:</small>
                            <div class="fw-bold text-info">
                                {% with avg_price=manufacturer.drug_set.aggregate.unit_price__avg %}
                                {{ avg_price|floatformat:2|default:"غير محدد" }} ريال
                                {% endwith %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Categories Distribution -->
                <div class="mt-4">
                    <h6 class="text-muted">توزيع التصنيفات:</h6>
                    <div class="row">
                        {% regroup manufacturer.drug_set.all by category as category_groups %}
                        {% for category_group in category_groups|slice:":4" %}
                        <div class="col-md-6 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">{{ category_group.grouper.name }}</span>
                                <span class="fw-bold">{{ category_group.list|length }} دواء</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
