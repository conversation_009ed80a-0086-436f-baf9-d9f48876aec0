# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('drugs', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('company_registration', models.CharField(max_length=100, unique=True, verbose_name='رقم السجل التجاري')),
                ('tax_number', models.CharField(blank=True, max_length=50, verbose_name='الرقم الضريبي')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('city', models.CharField(max_length=100, verbose_name='المدينة')),
                ('country', models.CharField(max_length=100, verbose_name='البلد')),
                ('phone', models.CharField(max_length=20, verbose_name='الهاتف')),
                ('email', models.EmailField(max_length=254, verbose_name='البريد الإلكتروني')),
                ('website', models.URLField(blank=True, verbose_name='الموقع الإلكتروني')),
                ('contact_person', models.CharField(max_length=200, verbose_name='الشخص المسؤول')),
                ('contact_phone', models.CharField(max_length=20, verbose_name='هاتف الشخص المسؤول')),
                ('contact_email', models.EmailField(blank=True, max_length=254, verbose_name='بريد الشخص المسؤول')),
                ('rating', models.DecimalField(decimal_places=2, default=0, max_digits=3, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='التقييم')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='الحد الائتماني')),
                ('payment_terms', models.CharField(blank=True, max_length=200, verbose_name='شروط الدفع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'المورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم أمر الشراء')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسل'), ('confirmed', 'مؤكد'), ('partially_received', 'مستلم جزئياً'), ('received', 'مستلم'), ('cancelled', 'ملغي')], default='draft', max_length=20, verbose_name='حالة الطلب')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('delivery_address', models.TextField(verbose_name='عنوان التسليم')),
                ('expected_delivery_date', models.DateField(verbose_name='تاريخ التسليم المتوقع')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المجموع الفرعي')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='قيمة الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='المجموع الكلي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_purchase_orders', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_purchase_orders', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_orders', to='procurement.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'أمر الشراء',
                'verbose_name_plural': 'أوامر الشراء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ordered_quantity', models.PositiveIntegerField(verbose_name='الكمية المطلوبة')),
                ('received_quantity', models.PositiveIntegerField(default=0, verbose_name='الكمية المستلمة')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='إجمالي السعر')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='drugs.drug', verbose_name='الدواء')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='procurement.purchaseorder', verbose_name='أمر الشراء')),
            ],
            options={
                'verbose_name': 'عنصر أمر الشراء',
                'verbose_name_plural': 'عناصر أمر الشراء',
                'unique_together': {('purchase_order', 'drug')},
            },
        ),
    ]
