# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'انخفاض المخزون'), ('expiry_soon', 'انتهاء صلاحية قريب'), ('expired_drugs', 'أدوية منتهية الصلاحية'), ('overdue_orders', 'طلبات متأخرة'), ('system_maintenance', 'صيانة النظام')], max_length=20, verbose_name='نوع التنبيه')),
                ('severity', models.CharField(choices=[('info', 'معلومات'), ('warning', 'تحذير'), ('error', 'خطأ'), ('critical', 'حرج')], max_length=10, verbose_name='مستوى الخطورة')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_resolved', models.BooleanField(default=False, verbose_name='تم الحل')),
                ('affected_count', models.PositiveIntegerField(default=0, verbose_name='عدد المتأثرين')),
                ('resolution_notes', models.TextField(blank=True, verbose_name='ملاحظات الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
            ],
            options={
                'verbose_name': 'تنبيه النظام',
                'verbose_name_plural': 'تنبيهات النظام',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('low_stock', 'انخفاض المخزون'), ('expiry_warning', 'تحذير انتهاء الصلاحية'), ('order_approval', 'موافقة الطلب'), ('order_rejection', 'رفض الطلب'), ('new_order', 'طلب جديد'), ('delivery_reminder', 'تذكير التسليم'), ('system_alert', 'تنبيه النظام')], max_length=20, verbose_name='نوع التنبيه')),
                ('priority', models.CharField(choices=[('low', 'منخفض'), ('normal', 'عادي'), ('high', 'عالي'), ('urgent', 'طارئ')], default='normal', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('is_sent', models.BooleanField(default=False, verbose_name='مرسل')),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='معرف الكائن المرتبط')),
                ('related_object_type', models.CharField(blank=True, max_length=50, verbose_name='نوع الكائن المرتبط')),
                ('action_url', models.URLField(blank=True, verbose_name='رابط الإجراء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستلم')),
            ],
            options={
                'verbose_name': 'التنبيه',
                'verbose_name_plural': 'التنبيهات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم القالب')),
                ('report_type', models.CharField(choices=[('inventory', 'تقرير المخزون'), ('sales', 'تقرير المبيعات'), ('orders', 'تقرير الطلبات'), ('expiry', 'تقرير انتهاء الصلاحية'), ('suppliers', 'تقرير الموردين'), ('financial', 'تقرير مالي'), ('usage', 'تقرير الاستخدام')], max_length=20, verbose_name='نوع التقرير')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('query_parameters', models.JSONField(default=dict, verbose_name='معاملات الاستعلام')),
                ('columns', models.JSONField(default=list, verbose_name='الأعمدة')),
                ('filters', models.JSONField(default=dict, verbose_name='المرشحات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_public', models.BooleanField(default=False, verbose_name='عام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
            ],
            options={
                'verbose_name': 'قالب التقرير',
                'verbose_name_plural': 'قوالب التقارير',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التقرير')),
                ('parameters', models.JSONField(default=dict, verbose_name='المعاملات المستخدمة')),
                ('file_path', models.FileField(blank=True, null=True, upload_to='reports/', verbose_name='ملف التقرير')),
                ('total_records', models.PositiveIntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('generation_time', models.DurationField(blank=True, null=True, verbose_name='وقت التوليد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التوليد')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='ولد بواسطة')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generated_reports', to='reports.reporttemplate', verbose_name='القالب')),
            ],
            options={
                'verbose_name': 'التقرير المولد',
                'verbose_name_plural': 'التقارير المولدة',
                'ordering': ['-created_at'],
            },
        ),
    ]
