from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from drugs.models import DrugCategory, Manufacturer, Drug
from inventory.models import Warehouse, DrugBatch, InventoryItem, InventoryMovement
from orders.models import Order, OrderItem, InternalRequest, InternalRequestItem
from procurement.models import Supplier, PurchaseOrder, PurchaseOrderItem
from accounts.models import UserProfile, Notification


class Command(BaseCommand):
    help = 'Generate demo data for the Drug Management System'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before generating new data',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            self.clear_data()

        self.stdout.write('Generating demo data...')

        # Generate data in order of dependencies
        self.create_users()
        self.create_drug_categories()
        self.create_manufacturers()
        self.create_drugs()
        self.create_warehouses()
        self.create_suppliers()
        self.create_batches()
        self.create_inventory_items()
        self.create_inventory_movements()
        self.create_orders()
        self.create_internal_requests()
        self.create_purchase_orders()
        self.create_notifications()

        self.stdout.write(
            self.style.SUCCESS('Successfully generated demo data!')
        )

    def clear_data(self):
        """Clear existing data"""
        models_to_clear = [
            Notification, PurchaseOrderItem, PurchaseOrder,
            InternalRequestItem, InternalRequest, OrderItem, Order,
            InventoryMovement, InventoryItem, DrugBatch, Supplier,
            Drug, Manufacturer, DrugCategory, Warehouse, UserProfile
        ]

        for model in models_to_clear:
            model.objects.all().delete()

        # Keep admin user, delete others
        User.objects.exclude(username='admin').delete()

    def create_users(self):
        """Create demo users"""
        self.stdout.write('Creating users...')

        users_data = [
            {'username': 'pharmacist1', 'first_name': 'أحمد', 'last_name': 'محمد', 'email': '<EMAIL>'},
            {'username': 'pharmacist2', 'first_name': 'فاطمة', 'last_name': 'علي', 'email': '<EMAIL>'},
            {'username': 'manager1', 'first_name': 'محمد', 'last_name': 'سالم', 'email': '<EMAIL>'},
            {'username': 'nurse1', 'first_name': 'سارة', 'last_name': 'أحمد', 'email': '<EMAIL>'},
            {'username': 'doctor1', 'first_name': 'خالد', 'last_name': 'يوسف', 'email': '<EMAIL>'},
        ]

        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'email': user_data['email'],
                    'is_staff': True,
                }
            )
            if created:
                user.set_password('password123')
                user.save()

                # Create user profile
                UserProfile.objects.create(
                    user=user,
                    phone=f'05{random.randint(10000000, 99999999)}',
                    department=random.choice(['الصيدلية', 'التمريض', 'الطب الباطني', 'الطوارئ']),
                    position=random.choice(['صيدلي', 'ممرض', 'طبيب', 'مدير']),
                )

    def create_drug_categories(self):
        """Create drug categories"""
        self.stdout.write('Creating drug categories...')

        categories = [
            {'name': 'مضادات حيوية', 'description': 'أدوية لعلاج العدوى البكتيرية'},
            {'name': 'مسكنات', 'description': 'أدوية لتسكين الألم'},
            {'name': 'أدوية القلب', 'description': 'أدوية لعلاج أمراض القلب والأوعية الدموية'},
            {'name': 'أدوية السكري', 'description': 'أدوية لعلاج مرض السكري'},
            {'name': 'أدوية الضغط', 'description': 'أدوية لعلاج ارتفاع ضغط الدم'},
            {'name': 'مضادات الالتهاب', 'description': 'أدوية لعلاج الالتهابات'},
            {'name': 'أدوية الجهاز التنفسي', 'description': 'أدوية لعلاج أمراض الجهاز التنفسي'},
            {'name': 'فيتامينات ومكملات', 'description': 'فيتامينات ومكملات غذائية'},
        ]

        for cat_data in categories:
            DrugCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )

    def create_manufacturers(self):
        """Create manufacturers"""
        self.stdout.write('Creating manufacturers...')

        manufacturers = [
            {'name': 'شركة الدواء السعودية', 'country': 'السعودية', 'contact_email': '<EMAIL>'},
            {'name': 'شركة فايزر', 'country': 'أمريكا', 'contact_email': '<EMAIL>'},
            {'name': 'شركة نوفارتيس', 'country': 'سويسرا', 'contact_email': '<EMAIL>'},
            {'name': 'شركة الجزيرة للأدوية', 'country': 'السعودية', 'contact_email': '<EMAIL>'},
            {'name': 'شركة روش', 'country': 'سويسرا', 'contact_email': '<EMAIL>'},
            {'name': 'شركة جونسون آند جونسون', 'country': 'أمريكا', 'contact_email': '<EMAIL>'},
            {'name': 'شركة نوفو نورديسك', 'country': 'الدنمارك', 'contact_email': '<EMAIL>'},
        ]

        for man_data in manufacturers:
            contact_info = f"البريد الإلكتروني: {man_data['contact_email']}\nالهاتف: +966{random.randint(100000000, 999999999)}\nالعنوان: عنوان {man_data['name']}"

            Manufacturer.objects.get_or_create(
                name=man_data['name'],
                defaults={
                    'country': man_data['country'],
                    'contact_info': contact_info,
                }
            )

    def create_drugs(self):
        """Create drugs"""
        self.stdout.write('Creating drugs...')

        drugs_data = [
            # مضادات حيوية
            {'name': 'أموكسيسيلين 500 مجم', 'category': 'مضادات حيوية', 'manufacturer': 'شركة الدواء السعودية', 'unit': 'كبسولة'},
            {'name': 'أزيثروميسين 250 مجم', 'category': 'مضادات حيوية', 'manufacturer': 'شركة فايزر', 'unit': 'قرص'},
            {'name': 'سيفالكسين 500 مجم', 'category': 'مضادات حيوية', 'manufacturer': 'شركة الجزيرة للأدوية', 'unit': 'كبسولة'},

            # مسكنات
            {'name': 'بانادول 500 مجم', 'category': 'مسكنات', 'manufacturer': 'شركة جونسون آند جونسون', 'unit': 'قرص'},
            {'name': 'إيبوبروفين 400 مجم', 'category': 'مسكنات', 'manufacturer': 'شركة فايزر', 'unit': 'قرص'},
            {'name': 'ديكلوفيناك 50 مجم', 'category': 'مسكنات', 'manufacturer': 'شركة نوفارتيس', 'unit': 'قرص'},

            # أدوية القلب
            {'name': 'أتينولول 50 مجم', 'category': 'أدوية القلب', 'manufacturer': 'شركة الدواء السعودية', 'unit': 'قرص'},
            {'name': 'ديجوكسين 0.25 مجم', 'category': 'أدوية القلب', 'manufacturer': 'شركة روش', 'unit': 'قرص'},

            # أدوية السكري
            {'name': 'جلوكوفاج 850 مجم', 'category': 'أدوية السكري', 'manufacturer': 'شركة نوفارتيس', 'unit': 'قرص'},
            {'name': 'أنسولين سريع المفعول', 'category': 'أدوية السكري', 'manufacturer': 'شركة نوفو نورديسك', 'unit': 'قلم'},
            {'name': 'جليبنكلاميد 5 مجم', 'category': 'أدوية السكري', 'manufacturer': 'شركة الدواء السعودية', 'unit': 'قرص'},

            # أدوية الضغط
            {'name': 'نورفاسك 5 مجم', 'category': 'أدوية الضغط', 'manufacturer': 'شركة فايزر', 'unit': 'قرص'},
            {'name': 'كابتوبريل 25 مجم', 'category': 'أدوية الضغط', 'manufacturer': 'شركة الجزيرة للأدوية', 'unit': 'قرص'},
            {'name': 'لوسارتان 50 مجم', 'category': 'أدوية الضغط', 'manufacturer': 'شركة نوفارتيس', 'unit': 'قرص'},

            # مضادات الالتهاب
            {'name': 'بريدنيزولون 5 مجم', 'category': 'مضادات الالتهاب', 'manufacturer': 'شركة روش', 'unit': 'قرص'},
            {'name': 'هيدروكورتيزون كريم', 'category': 'مضادات الالتهاب', 'manufacturer': 'شركة جونسون آند جونسون', 'unit': 'أنبوب'},

            # أدوية الجهاز التنفسي
            {'name': 'سالبوتامول بخاخ', 'category': 'أدوية الجهاز التنفسي', 'manufacturer': 'شركة الدواء السعودية', 'unit': 'بخاخ'},
            {'name': 'مونتيلوكاست 10 مجم', 'category': 'أدوية الجهاز التنفسي', 'manufacturer': 'شركة نوفارتيس', 'unit': 'قرص'},

            # فيتامينات
            {'name': 'فيتامين د 1000 وحدة', 'category': 'فيتامينات ومكملات', 'manufacturer': 'شركة الجزيرة للأدوية', 'unit': 'كبسولة'},
            {'name': 'فيتامين ب المركب', 'category': 'فيتامينات ومكملات', 'manufacturer': 'شركة فايزر', 'unit': 'قرص'},
            {'name': 'حديد + فوليك أسيد', 'category': 'فيتامينات ومكملات', 'manufacturer': 'شركة الدواء السعودية', 'unit': 'قرص'},
        ]

        categories = {cat.name: cat for cat in DrugCategory.objects.all()}
        manufacturers = {man.name: man for man in Manufacturer.objects.all()}

        for drug_data in drugs_data:
            # Extract strength from name if available
            strength = ''
            if 'مجم' in drug_data['name']:
                parts = drug_data['name'].split()
                for i, part in enumerate(parts):
                    if 'مجم' in part and i > 0:
                        strength = f"{parts[i-1]} {part}"
                        break

            # Map unit to choices
            unit_mapping = {
                'قرص': 'tablet',
                'كبسولة': 'capsule',
                'أنبوب': 'tube',
                'بخاخ': 'bottle',
                'قلم': 'vial'
            }
            unit_choice = unit_mapping.get(drug_data['unit'], 'tablet')

            Drug.objects.get_or_create(
                scientific_name=drug_data['name'],
                defaults={
                    'commercial_name': drug_data['name'],
                    'category': categories[drug_data['category']],
                    'manufacturer': manufacturers[drug_data['manufacturer']],
                    'unit': unit_choice,
                    'unit_price': Decimal(str(random.uniform(1.0, 100.0))),
                    'strength': strength,
                    'dosage_form': drug_data['unit'],
                    'description': f'وصف {drug_data["name"]}',
                    'requires_prescription': random.choice([True, False]),
                    'is_controlled': random.choice([True, False]) if 'مضادات حيوية' in drug_data['category'] else False,
                }
            )

    def create_warehouses(self):
        """Create warehouses"""
        self.stdout.write('Creating warehouses...')

        warehouses = [
            {
                'name': 'المستودع الرئيسي',
                'location': 'الدور الأرضي - المبنى الرئيسي',
                'capacity': 10000,
                'temperature_controlled': True,
                'description': 'المستودع الرئيسي لتخزين جميع أنواع الأدوية'
            },
            {
                'name': 'مستودع الطوارئ',
                'location': 'قسم الطوارئ - الدور الأول',
                'capacity': 2000,
                'temperature_controlled': True,
                'description': 'مستودع خاص بأدوية الطوارئ'
            },
            {
                'name': 'مستودع الأقسام الداخلية',
                'location': 'الدور الثاني - جناح الأقسام الداخلية',
                'capacity': 3000,
                'temperature_controlled': False,
                'description': 'مستودع لأدوية الأقسام الداخلية'
            },
            {
                'name': 'مستودع العيادات الخارجية',
                'location': 'مبنى العيادات الخارجية',
                'capacity': 1500,
                'temperature_controlled': False,
                'description': 'مستودع خاص بالعيادات الخارجية'
            },
        ]

        for warehouse_data in warehouses:
            Warehouse.objects.get_or_create(
                name=warehouse_data['name'],
                defaults=warehouse_data
            )

    def create_suppliers(self):
        """Create suppliers"""
        self.stdout.write('Creating suppliers...')

        suppliers = [
            {
                'name': 'شركة الدواء السعودية للتوريد',
                'contact_person': 'أحمد محمد العلي',
                'email': '<EMAIL>',
                'phone': '+966112345678',
                'address': 'الرياض، المملكة العربية السعودية',
                'supplier_type': 'local',
                'rating': 4.8,
                'is_active': True,
            },
            {
                'name': 'شركة فايزر الشرق الأوسط',
                'contact_person': 'سارة أحمد',
                'email': '<EMAIL>',
                'phone': '+966112345679',
                'address': 'دبي، الإمارات العربية المتحدة',
                'supplier_type': 'international',
                'rating': 4.5,
                'is_active': True,
            },
            {
                'name': 'مؤسسة الجزيرة الطبية',
                'contact_person': 'محمد سالم',
                'email': '<EMAIL>',
                'phone': '+966112345680',
                'address': 'جدة، المملكة العربية السعودية',
                'supplier_type': 'local',
                'rating': 4.2,
                'is_active': True,
            },
            {
                'name': 'شركة نوفارتيس الخليج',
                'contact_person': 'فاطمة علي',
                'email': '<EMAIL>',
                'phone': '+966112345681',
                'address': 'الكويت',
                'supplier_type': 'regional',
                'rating': 4.6,
                'is_active': True,
            },
        ]

        for supplier_data in suppliers:
            Supplier.objects.get_or_create(
                name=supplier_data['name'],
                defaults=supplier_data
            )

    def create_batches(self):
        """Create batches for drugs"""
        self.stdout.write('Creating batches...')

        drugs = Drug.objects.all()
        warehouses = Warehouse.objects.all()

        for drug in drugs:
            # Create 2-4 batches per drug
            for i in range(random.randint(2, 4)):
                batch_number = f"{drug.name[:3].upper()}-2024-{str(i+1).zfill(3)}"

                # Random expiry date (6 months to 3 years from now)
                expiry_date = timezone.now().date() + timedelta(
                    days=random.randint(180, 1095)
                )

                # Random manufacturing date (1-6 months ago)
                manufacturing_date = timezone.now().date() - timedelta(
                    days=random.randint(30, 180)
                )

                DrugBatch.objects.get_or_create(
                    batch_number=batch_number,
                    defaults={
                        'drug': drug,
                        'manufacturing_date': manufacturing_date,
                        'expiry_date': expiry_date,
                        'cost_price': Decimal(str(random.uniform(1.0, 100.0))),
                    }
                )

    def create_inventory_items(self):
        """Create inventory items"""
        self.stdout.write('Creating inventory items...')

        drugs = Drug.objects.all()
        warehouses = Warehouse.objects.all()

        for drug in drugs:
            for warehouse in warehouses:
                # Not all drugs are in all warehouses
                if random.choice([True, False, True]):  # 66% chance
                    quantity = random.randint(0, 500)
                    min_quantity = random.randint(10, 50)
                    max_quantity = random.randint(100, 1000)

                    InventoryItem.objects.get_or_create(
                        drug=drug,
                        warehouse=warehouse,
                        defaults={
                            'quantity': quantity,
                            'min_quantity': min_quantity,
                            'max_quantity': max_quantity,
                            'unit_cost': Decimal(str(random.uniform(1.0, 100.0))),
                            'location': f'رف {random.randint(1, 20)}-{random.randint(1, 10)}',
                        }
                    )

    def create_inventory_movements(self):
        """Create inventory movements"""
        self.stdout.write('Creating inventory movements...')

        inventory_items = InventoryItem.objects.all()
        users = User.objects.all()
        movement_types = ['in', 'out', 'transfer', 'adjustment']

        for _ in range(200):  # Create 200 movements
            item = random.choice(inventory_items)
            movement_type = random.choice(movement_types)

            if movement_type == 'in':
                quantity = random.randint(10, 100)
                reason = 'استلام من المورد'
            elif movement_type == 'out':
                quantity = -random.randint(1, min(50, item.quantity))
                reason = 'صرف للمريض'
            elif movement_type == 'transfer':
                quantity = random.randint(-20, 20)
                reason = 'نقل بين المستودعات'
            else:  # adjustment
                quantity = random.randint(-10, 10)
                reason = 'تسوية جرد'

            # Random date within last 3 months
            movement_date = timezone.now() - timedelta(
                days=random.randint(0, 90)
            )

            InventoryMovement.objects.create(
                inventory_item=item,
                movement_type=movement_type,
                quantity=quantity,
                reason=reason,
                created_by=random.choice(users),
                created_at=movement_date,
                notes=f'حركة {movement_type} لـ {item.drug.name}',
            )

    def create_orders(self):
        """Create orders"""
        self.stdout.write('Creating orders...')

        users = User.objects.all()
        drugs = Drug.objects.all()
        statuses = ['pending', 'approved', 'fulfilled', 'cancelled']
        priorities = ['low', 'normal', 'high', 'urgent']
        institutions = [
            'مستشفى الملك فهد',
            'مركز الرعاية الأولية',
            'مستشفى الأطفال',
            'مستشفى النساء والولادة',
            'مركز الأورام',
        ]

        for i in range(50):  # Create 50 orders
            order_number = f"ORD-2024-{str(i+1).zfill(3)}"

            # Random date within last 6 months
            order_date = timezone.now() - timedelta(
                days=random.randint(0, 180)
            )

            order = Order.objects.create(
                order_number=order_number,
                institution=random.choice(institutions),
                department=random.choice(['الطوارئ', 'الأقسام الداخلية', 'العيادات الخارجية', 'العناية المركزة']),
                requested_by=random.choice(users),
                status=random.choice(statuses),
                priority=random.choice(priorities),
                notes=f'طلب رقم {order_number}',
                created_at=order_date,
            )

            # Add order items (2-8 items per order)
            order_drugs = random.sample(list(drugs), random.randint(2, 8))
            for drug in order_drugs:
                OrderItem.objects.create(
                    order=order,
                    drug=drug,
                    quantity_requested=random.randint(1, 50),
                    quantity_approved=random.randint(0, 50) if order.status in ['approved', 'fulfilled'] else 0,
                    unit_cost=Decimal(str(random.uniform(1.0, 100.0))),
                    notes=f'صنف {drug.name} في الطلب {order_number}',
                )

    def create_internal_requests(self):
        """Create internal requests"""
        self.stdout.write('Creating internal requests...')

        users = User.objects.all()
        drugs = Drug.objects.all()
        warehouses = Warehouse.objects.all()
        statuses = ['pending', 'approved', 'fulfilled', 'rejected']

        for i in range(30):  # Create 30 internal requests
            request_number = f"INT-2024-{str(i+1).zfill(3)}"

            # Random date within last 3 months
            request_date = timezone.now() - timedelta(
                days=random.randint(0, 90)
            )

            request = InternalRequest.objects.create(
                request_number=request_number,
                from_warehouse=random.choice(warehouses),
                to_warehouse=random.choice(warehouses),
                requested_by=random.choice(users),
                status=random.choice(statuses),
                notes=f'طلب داخلي رقم {request_number}',
                created_at=request_date,
            )

            # Add request items (1-5 items per request)
            request_drugs = random.sample(list(drugs), random.randint(1, 5))
            for drug in request_drugs:
                InternalRequestItem.objects.create(
                    request=request,
                    drug=drug,
                    quantity_requested=random.randint(1, 20),
                    quantity_approved=random.randint(0, 20) if request.status in ['approved', 'fulfilled'] else 0,
                    notes=f'صنف {drug.name} في الطلب الداخلي {request_number}',
                )

    def create_purchase_orders(self):
        """Create purchase orders"""
        self.stdout.write('Creating purchase orders...')

        users = User.objects.all()
        drugs = Drug.objects.all()
        suppliers = Supplier.objects.all()
        statuses = ['draft', 'sent', 'confirmed', 'received', 'cancelled']

        for i in range(25):  # Create 25 purchase orders
            po_number = f"PO-2024-{str(i+1).zfill(3)}"

            # Random date within last 4 months
            po_date = timezone.now() - timedelta(
                days=random.randint(0, 120)
            )

            # Expected delivery date (1-4 weeks from order date)
            expected_delivery = po_date + timedelta(
                days=random.randint(7, 28)
            )

            po = PurchaseOrder.objects.create(
                po_number=po_number,
                supplier=random.choice(suppliers),
                created_by=random.choice(users),
                status=random.choice(statuses),
                expected_delivery_date=expected_delivery.date(),
                notes=f'أمر شراء رقم {po_number}',
                created_at=po_date,
            )

            # Add purchase order items (3-10 items per order)
            po_drugs = random.sample(list(drugs), random.randint(3, 10))
            for drug in po_drugs:
                unit_cost = Decimal(str(random.uniform(1.0, 100.0)))
                quantity = random.randint(50, 500)

                PurchaseOrderItem.objects.create(
                    purchase_order=po,
                    drug=drug,
                    quantity=quantity,
                    unit_cost=unit_cost,
                    total_cost=unit_cost * quantity,
                    notes=f'صنف {drug.name} في أمر الشراء {po_number}',
                )

    def create_notifications(self):
        """Create notifications"""
        self.stdout.write('Creating notifications...')

        users = User.objects.all()
        notification_types = ['info', 'warning', 'error', 'success']

        notifications_data = [
            {'title': 'مخزون منخفض', 'message': 'يوجد 15 صنف دواء بمخزون منخفض', 'type': 'warning'},
            {'title': 'طلب جديد', 'message': 'تم استلام طلب جديد من مستشفى الملك فهد', 'type': 'info'},
            {'title': 'انتهاء صلاحية', 'message': 'يوجد 5 أصناف ستنتهي صلاحيتها خلال أسبوع', 'type': 'error'},
            {'title': 'تم الموافقة', 'message': 'تم الموافقة على أمر الشراء PO-2024-001', 'type': 'success'},
            {'title': 'استلام شحنة', 'message': 'تم استلام شحنة جديدة من شركة الدواء السعودية', 'type': 'success'},
            {'title': 'تحديث النظام', 'message': 'تم تحديث النظام إلى الإصدار الجديد', 'type': 'info'},
            {'title': 'نفاد مخزون', 'message': 'نفد مخزون دواء الأنسولين من المستودع الرئيسي', 'type': 'error'},
            {'title': 'طلب عاجل', 'message': 'طلب عاجل من قسم الطوارئ يحتاج موافقة فورية', 'type': 'warning'},
        ]

        for user in users:
            # Create 3-8 notifications per user
            user_notifications = random.sample(notifications_data, random.randint(3, 8))
            for notif_data in user_notifications:
                # Random date within last month
                notif_date = timezone.now() - timedelta(
                    days=random.randint(0, 30)
                )

                Notification.objects.create(
                    user=user,
                    title=notif_data['title'],
                    message=notif_data['message'],
                    notification_type=notif_data['type'],
                    is_read=random.choice([True, False]),
                    created_at=notif_date,
                )
