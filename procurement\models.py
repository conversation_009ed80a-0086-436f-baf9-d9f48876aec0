from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from drugs.models import Drug

class Supplier(models.Model):
    """الموردين"""
    name = models.CharField(max_length=200, verbose_name="اسم المورد")
    company_registration = models.CharField(max_length=100, unique=True, verbose_name="رقم السجل التجاري")
    tax_number = models.CharField(max_length=50, blank=True, verbose_name="الرقم الضريبي")

    # معلومات الاتصال
    address = models.TextField(verbose_name="العنوان")
    city = models.CharField(max_length=100, verbose_name="المدينة")
    country = models.CharField(max_length=100, verbose_name="البلد")
    phone = models.CharField(max_length=20, verbose_name="الهاتف")
    email = models.EmailField(verbose_name="البريد الإلكتروني")
    website = models.URLField(blank=True, verbose_name="الموقع الإلكتروني")

    # معلومات الاتصال
    contact_person = models.CharField(max_length=200, verbose_name="الشخص المسؤول")
    contact_phone = models.CharField(max_length=20, verbose_name="هاتف الشخص المسؤول")
    contact_email = models.EmailField(blank=True, verbose_name="بريد الشخص المسؤول")

    # معلومات التقييم
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=0,
                               validators=[MinValueValidator(Decimal('0'))],
                               verbose_name="التقييم")
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="الحد الائتماني")
    payment_terms = models.CharField(max_length=200, blank=True, verbose_name="شروط الدفع")

    # حالة المورد
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_approved = models.BooleanField(default=False, verbose_name="معتمد")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "المورد"
        verbose_name_plural = "الموردين"
        ordering = ['name']

    def __str__(self):
        return self.name

class PurchaseOrder(models.Model):
    """أوامر الشراء"""
    ORDER_STATUS = [
        ('draft', 'مسودة'),
        ('sent', 'مرسل'),
        ('confirmed', 'مؤكد'),
        ('partially_received', 'مستلم جزئياً'),
        ('received', 'مستلم'),
        ('cancelled', 'ملغي'),
    ]

    # معلومات أساسية
    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم أمر الشراء")
    supplier = models.ForeignKey(Supplier, on_delete=models.PROTECT, related_name='purchase_orders', verbose_name="المورد")

    # حالة الطلب
    status = models.CharField(max_length=20, choices=ORDER_STATUS, default='draft', verbose_name="حالة الطلب")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    delivery_address = models.TextField(verbose_name="عنوان التسليم")
    expected_delivery_date = models.DateField(verbose_name="تاريخ التسليم المتوقع")

    # معلومات المالية
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="المجموع الفرعي")
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="قيمة الضريبة")
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="المجموع الكلي")

    # معلومات المستخدم
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='created_purchase_orders', verbose_name="أنشأ بواسطة")
    approved_by = models.ForeignKey(User, on_delete=models.PROTECT, null=True, blank=True, related_name='approved_purchase_orders', verbose_name="وافق عليه")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإرسال")

    class Meta:
        verbose_name = "أمر الشراء"
        verbose_name_plural = "أوامر الشراء"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.supplier.name}"

    def generate_order_number(self):
        """توليد رقم أمر الشراء"""
        from django.utils import timezone
        today = timezone.now().date()
        count = PurchaseOrder.objects.filter(created_at__date=today).count() + 1
        return f"PO-{today.strftime('%Y%m%d')}-{count:04d}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

class PurchaseOrderItem(models.Model):
    """عناصر أمر الشراء"""
    purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='items', verbose_name="أمر الشراء")
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, verbose_name="الدواء")

    # الكميات
    ordered_quantity = models.PositiveIntegerField(verbose_name="الكمية المطلوبة")
    received_quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية المستلمة")

    # الأسعار
    unit_price = models.DecimalField(max_digits=10, decimal_places=2,
                                   validators=[MinValueValidator(Decimal('0.01'))],
                                   verbose_name="سعر الوحدة")
    total_price = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="إجمالي السعر")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر أمر الشراء"
        verbose_name_plural = "عناصر أمر الشراء"
        unique_together = ['purchase_order', 'drug']

    def __str__(self):
        return f"{self.purchase_order.order_number} - {self.drug.commercial_name}"

    def save(self, *args, **kwargs):
        self.total_price = self.ordered_quantity * self.unit_price
        super().save(*args, **kwargs)

    @property
    def is_fully_received(self):
        """فحص اكتمال الاستلام"""
        return self.received_quantity >= self.ordered_quantity

    @property
    def remaining_quantity(self):
        """الكمية المتبقية للاستلام"""
        return self.ordered_quantity - self.received_quantity
