{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ drug.commercial_name }} - تفاصيل الدواء{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:drug_list' %}">قائمة الأدوية</a></li>
        <li class="breadcrumb-item active">{{ drug.commercial_name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-pills me-2"></i>
                {{ drug.commercial_name }}
                {% if drug.is_controlled %}
                <span class="badge bg-warning text-dark ms-2">دواء مراقب</span>
                {% endif %}
                {% if not drug.is_active %}
                <span class="badge bg-secondary ms-2">غير نشط</span>
                {% endif %}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'drugs:drug_edit' drug.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <a href="{% url 'drugs:drug_delete' drug.pk %}" class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                    <i class="fas fa-trash me-2"></i>
                    حذف
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Drug Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم التجاري</label>
                        <p class="fw-bold">{{ drug.commercial_name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم العلمي</label>
                        <p class="fw-bold">{{ drug.scientific_name }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">التصنيف</label>
                        <p>
                            <span class="badge bg-primary">{{ drug.category.name }}</span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الشركة المصنعة</label>
                        <p>{{ drug.manufacturer.name }} - {{ drug.manufacturer.country }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">الوحدة</label>
                        <p>{{ drug.get_unit_display }}</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">سعر الوحدة</label>
                        <p class="fw-bold text-success">{{ drug.unit_price }} ريال</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label text-muted">التركيز</label>
                        <p>{{ drug.strength|default:"غير محدد" }}</p>
                    </div>
                    {% if drug.dosage_form %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الشكل الصيدلاني</label>
                        <p>{{ drug.dosage_form }}</p>
                    </div>
                    {% endif %}
                    {% if drug.barcode %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الباركود</label>
                        <p><code>{{ drug.barcode }}</code></p>
                    </div>
                    {% endif %}
                    {% if drug.description %}
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">الوصف</label>
                        <p>{{ drug.description }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Drug Properties -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    خصائص الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <span class="text-muted">الحالة:</span>
                            <span class="ms-2">
                                {% if drug.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-prescription-bottle-alt me-2 {% if drug.requires_prescription %}text-warning{% else %}text-muted{% endif %}"></i>
                            <span class="text-muted">وصفة طبية:</span>
                            <span class="ms-2">
                                {% if drug.requires_prescription %}
                                <span class="badge bg-warning text-dark">مطلوبة</span>
                                {% else %}
                                <span class="badge bg-info">غير مطلوبة</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-shield-alt me-2 {% if drug.is_controlled %}text-danger{% else %}text-muted{% endif %}"></i>
                            <span class="text-muted">دواء مراقب:</span>
                            <span class="ms-2">
                                {% if drug.is_controlled %}
                                <span class="badge bg-danger">نعم</span>
                                {% else %}
                                <span class="badge bg-success">لا</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    معلومات المخزون
                </h5>
            </div>
            <div class="card-body">
                {% if drug.inventory_items.exists %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>المستودع</th>
                                <th>الكمية المتاحة</th>
                                <th>الكمية المحجوزة</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in drug.inventory_items.all %}
                            <tr>
                                <td>{{ item.warehouse.name }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.reserved_quantity }}</td>
                                <td>{{ item.minimum_stock }}</td>
                                <td>
                                    {% if item.is_out_of_stock %}
                                    <span class="badge bg-danger">نفد المخزون</span>
                                    {% elif item.is_low_stock %}
                                    <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                    {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">لا توجد معلومات مخزون</h6>
                    <p class="text-muted small">لم يتم إضافة هذا الدواء إلى أي مستودع بعد.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة إلى المخزون
                    </a>
                    <a href="#" class="btn btn-outline-success">
                        <i class="fas fa-shopping-cart me-2"></i>
                        إنشاء طلب شراء
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-chart-line me-2"></i>
                        عرض التقارير
                    </a>
                    <a href="#" class="btn btn-outline-warning">
                        <i class="fas fa-history me-2"></i>
                        سجل الحركات
                    </a>
                </div>
            </div>
        </div>

        <!-- Drug Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ drug.inventory_items.count }}</h4>
                            <small class="text-muted">مستودع</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">
                            {% with total_stock=drug.inventory_items.all|length %}
                                {{ total_stock|default:0 }}
                            {% endwith %}
                        </h4>
                        <small class="text-muted">إجمالي المخزون</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Drug Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">تاريخ الإضافة:</span>
                        <span>{{ drug.created_at|date:"d/m/Y" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">آخر تحديث:</span>
                        <span>{{ drug.updated_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">رقم الدواء:</span>
                        <span><code>#{{ drug.id }}</code></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Example: Add tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
