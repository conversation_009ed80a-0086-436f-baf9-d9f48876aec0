from django.contrib import admin
from .models import DrugCategory, Manufacturer, Drug

@admin.register(DrugCategory)
class DrugCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'created_at']
    search_fields = ['name']
    ordering = ['name']

@admin.register(Manufacturer)
class ManufacturerAdmin(admin.ModelAdmin):
    list_display = ['name', 'country', 'is_active', 'created_at']
    list_filter = ['country', 'is_active']
    search_fields = ['name', 'country']
    ordering = ['name']

@admin.register(Drug)
class DrugAdmin(admin.ModelAdmin):
    list_display = ['commercial_name', 'scientific_name', 'category', 'manufacturer', 'unit_price', 'is_active']
    list_filter = ['category', 'manufacturer', 'unit', 'is_active', 'requires_prescription', 'is_controlled']
    search_fields = ['commercial_name', 'scientific_name', 'barcode']
    ordering = ['commercial_name']
    readonly_fields = ['created_at', 'updated_at']
