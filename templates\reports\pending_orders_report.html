{% extends 'base/base.html' %}
{% load static %}

{% block title %}تقرير الطلبات المعلقة - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">تقرير الطلبات المعلقة</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-hourglass-half text-warning me-2"></i>
                تقرير الطلبات المعلقة
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Alert Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning border-left-warning">
            <h4 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تنبيه: طلبات في انتظار المعالجة
            </h4>
            <p class="mb-2">
                يوجد <strong>185 طلب</strong> في انتظار المعالجة، منها <strong>45 طلب</strong> متأخر عن الموعد المحدد.
                <strong>28 طلب</strong> منها عاجل ويحتاج معالجة فورية.
            </p>
            <hr>
            <p class="mb-0">
                <i class="fas fa-info-circle me-2"></i>
                يُنصح بمراجعة الطلبات العاجلة والمتأخرة أولاً لضمان استمرارية الخدمة.
            </p>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">185</h4>
                <small class="text-muted">إجمالي الطلبات المعلقة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">28</h4>
                <small class="text-muted">طلبات عاجلة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">45</h4>
                <small class="text-muted">طلبات متأخرة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-secondary">
            <div class="card-body">
                <h4 class="text-secondary mb-1">112</h4>
                <small class="text-muted">طلبات عادية</small>
            </div>
        </div>
    </div>
</div>

<!-- Priority Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-fire me-2"></i>
                    طلبات عاجلة - تحتاج معالجة فورية
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>المؤسسة</th>
                                <th>تاريخ الطلب</th>
                                <th>عدد الأصناف</th>
                                <th>الأولوية</th>
                                <th>أيام التأخير</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-danger">
                                <td><strong>ORD-2024-001</strong></td>
                                <td>مستشفى الملك فهد - قسم الطوارئ</td>
                                <td>2024-12-10</td>
                                <td>8</td>
                                <td>
                                    <span class="badge bg-danger">عاجل جداً</span>
                                </td>
                                <td>
                                    <span class="badge bg-danger">5 أيام</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="approveOrder('ORD-2024-001')">
                                        <i class="fas fa-check me-1"></i>
                                        موافقة
                                    </button>
                                </td>
                            </tr>
                            <tr class="table-danger">
                                <td><strong>ORD-2024-002</strong></td>
                                <td>مركز الرعاية الأولية</td>
                                <td>2024-12-11</td>
                                <td>12</td>
                                <td>
                                    <span class="badge bg-danger">عاجل</span>
                                </td>
                                <td>
                                    <span class="badge bg-danger">4 أيام</span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="approveOrder('ORD-2024-002')">
                                        <i class="fas fa-check me-1"></i>
                                        موافقة
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Orders -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    طلبات متأخرة - تجاوزت الموعد المحدد
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>المؤسسة</th>
                                <th>تاريخ الطلب</th>
                                <th>الموعد المتوقع</th>
                                <th>أيام التأخير</th>
                                <th>السبب</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-warning">
                                <td><strong>ORD-2024-003</strong></td>
                                <td>مستشفى الأطفال</td>
                                <td>2024-12-08</td>
                                <td>2024-12-12</td>
                                <td>
                                    <span class="badge bg-warning text-dark">3 أيام</span>
                                </td>
                                <td>
                                    <small class="text-muted">في انتظار موافقة المدير</small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="followUp('ORD-2024-003')">
                                        <i class="fas fa-phone me-1"></i>
                                        متابعة
                                    </button>
                                </td>
                            </tr>
                            <tr class="table-warning">
                                <td><strong>ORD-2024-004</strong></td>
                                <td>مركز الرعاية الأولية</td>
                                <td>2024-12-09</td>
                                <td>2024-12-13</td>
                                <td>
                                    <span class="badge bg-warning text-dark">2 أيام</span>
                                </td>
                                <td>
                                    <small class="text-muted">نقص في المخزون</small>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-info" onclick="checkStock('ORD-2024-004')">
                                        <i class="fas fa-search me-1"></i>
                                        فحص المخزون
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الطلبات المعلقة حسب الأولوية
                </h6>
            </div>
            <div class="card-body">
                <canvas id="priorityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    الطلبات المعلقة حسب المؤسسة
                </h6>
            </div>
            <div class="card-body">
                <canvas id="institutionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- All Pending Orders -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    جميع الطلبات المعلقة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>المؤسسة</th>
                                <th>تاريخ الطلب</th>
                                <th>عدد الأصناف</th>
                                <th>القيمة</th>
                                <th>الحالة</th>
                                <th>المسؤول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ORD-2024-005</strong></td>
                                <td>مستشفى الملك فهد</td>
                                <td>2024-12-14</td>
                                <td>15</td>
                                <td>45,000 ريال</td>
                                <td><span class="badge bg-warning">في المراجعة</span></td>
                                <td>أحمد محمد</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-info" onclick="viewOrder('ORD-2024-005')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="approveOrder('ORD-2024-005')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="rejectOrder('ORD-2024-005')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>ORD-2024-006</strong></td>
                                <td>مركز الرعاية الأولية</td>
                                <td>2024-12-14</td>
                                <td>8</td>
                                <td>22,500 ريال</td>
                                <td><span class="badge bg-info">في الانتظار</span></td>
                                <td>فاطمة علي</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-info" onclick="viewOrder('ORD-2024-006')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="approveOrder('ORD-2024-006')">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="rejectOrder('ORD-2024-006')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }
}

.table-danger {
    --bs-table-accent-bg: rgba(220, 53, 69, 0.1);
}

.table-warning {
    --bs-table-accent-bg: rgba(255, 193, 7, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Priority Chart
    const priorityCtx = document.getElementById('priorityChart').getContext('2d');
    new Chart(priorityCtx, {
        type: 'doughnut',
        data: {
            labels: ['عاجل جداً', 'عاجل', 'عادي', 'غير عاجل'],
            datasets: [{
                data: [28, 45, 85, 27],
                backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Institution Chart
    const institutionCtx = document.getElementById('institutionChart').getContext('2d');
    new Chart(institutionCtx, {
        type: 'bar',
        data: {
            labels: ['مستشفى الملك فهد', 'مركز الرعاية الأولية', 'مستشفى الأطفال', 'مستشفى النساء'],
            datasets: [{
                label: 'عدد الطلبات المعلقة',
                data: [65, 45, 38, 37],
                backgroundColor: '#ffc107'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function approveOrder(orderId) {
    if (confirm('هل تريد الموافقة على هذا الطلب؟')) {
        showToast(`تم الموافقة على الطلب ${orderId}`, 'success');
        // Implementation for order approval
    }
}

function rejectOrder(orderId) {
    if (confirm('هل تريد رفض هذا الطلب؟')) {
        showToast(`تم رفض الطلب ${orderId}`, 'warning');
        // Implementation for order rejection
    }
}

function viewOrder(orderId) {
    showToast(`عرض تفاصيل الطلب ${orderId}`, 'info');
    // Implementation for viewing order details
}

function followUp(orderId) {
    showToast(`تم إرسال تذكير للطلب ${orderId}`, 'info');
    // Implementation for follow up
}

function checkStock(orderId) {
    showToast(`فحص المخزون للطلب ${orderId}`, 'info');
    // Implementation for stock check
}

function exportToExcel() {
    showToast('جاري تصدير التقرير إلى Excel...', 'info');
}

function exportToPDF() {
    showToast('جاري تصدير التقرير إلى PDF...', 'info');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
