from django.urls import path
from . import views

app_name = 'institutions'

urlpatterns = [
    # Institution URLs
    path('', views.InstitutionListView.as_view(), name='institution_list'),
    path('create/', views.InstitutionCreateView.as_view(), name='institution_create'),
    path('<int:pk>/', views.InstitutionDetailView.as_view(), name='institution_detail'),
    path('<int:pk>/edit/', views.InstitutionUpdateView.as_view(), name='institution_edit'),
    path('<int:pk>/delete/', views.InstitutionDeleteView.as_view(), name='institution_delete'),
    
    # Department URLs
    path('departments/', views.DepartmentListView.as_view(), name='department_list'),
    path('departments/create/', views.DepartmentCreateView.as_view(), name='department_create'),
    path('departments/<int:pk>/', views.DepartmentDetailView.as_view(), name='department_detail'),
    path('departments/<int:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_edit'),
    path('departments/<int:pk>/delete/', views.DepartmentDeleteView.as_view(), name='department_delete'),
    
    # API URLs
    path('api/<int:institution_id>/departments/', views.institution_departments_api, name='institution_departments_api'),
]
