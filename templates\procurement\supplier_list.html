{% extends 'base/base.html' %}
{% load static %}

{% block title %}الموردين - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'procurement:purchase_order_list' %}">المشتريات</a></li>
        <li class="breadcrumb-item active">الموردين</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-truck me-2"></i>
                الموردين
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'procurement:supplier_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    مورد جديد
                </a>
                <a href="{% url 'procurement:purchase_order_list' %}" class="btn btn-outline-info">
                    <i class="fas fa-shopping-cart me-2"></i>
                    أوامر الشراء
                </a>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-file-export me-2"></i>
                        التقارير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'procurement:supplier_performance_report' %}">تقرير أداء الموردين</a></li>
                        <li><a class="dropdown-item" href="#">قائمة الموردين المعتمدين</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">تصدير Excel</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ suppliers|length }}</h4>
                <small class="text-muted">إجمالي الموردين</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with active_count=0 %}
                        {% for supplier in suppliers %}
                            {% if supplier.is_active %}
                                {% with active_count=active_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ active_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">نشط</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% with approved_count=0 %}
                        {% for supplier in suppliers %}
                            {% if supplier.is_approved %}
                                {% with approved_count=approved_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ approved_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">معتمد</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with pending_count=0 %}
                        {% for supplier in suppliers %}
                            {% if not supplier.is_approved %}
                                {% with pending_count=pending_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ pending_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">في الانتظار</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.GET.search }}" placeholder="اسم المورد أو رقم السجل...">
                    </div>
                    <div class="col-md-3">
                        <label for="city" class="form-label">المدينة</label>
                        <select class="form-select" id="city" name="city">
                            <option value="">جميع المدن</option>
                            <!-- Add city options here -->
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>نشط</option>
                            <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>معتمد</option>
                            <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>في الانتظار</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Suppliers List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الموردين ({{ suppliers|length }} مورد)
                </h5>
            </div>
            <div class="card-body">
                {% if suppliers %}
                <div class="row">
                    {% for supplier in suppliers %}
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 {% if not supplier.is_active %}border-secondary{% elif supplier.is_approved %}border-success{% else %}border-warning{% endif %}">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">{{ supplier.name }}</h6>
                                <div>
                                    {% if supplier.is_approved %}
                                    <span class="badge bg-success">معتمد</span>
                                    {% else %}
                                    <span class="badge bg-warning text-dark">في الانتظار</span>
                                    {% endif %}
                                    {% if not supplier.is_active %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">رقم السجل التجاري:</small>
                                    <div><code>{{ supplier.company_registration }}</code></div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">الموقع:</small>
                                    <div>{{ supplier.city }}, {{ supplier.country }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">الاتصال:</small>
                                    <div>
                                        <i class="fas fa-phone me-1"></i>{{ supplier.phone }}
                                        <br>
                                        <i class="fas fa-envelope me-1"></i>{{ supplier.email }}
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">الشخص المسؤول:</small>
                                    <div>{{ supplier.contact_person }}</div>
                                </div>
                                
                                <div class="mb-3">
                                    <small class="text-muted">التقييم:</small>
                                    <div class="text-warning">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= supplier.rating %}
                                            <i class="fas fa-star"></i>
                                            {% else %}
                                            <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="text-muted">({{ supplier.rating }})</span>
                                    </div>
                                </div>
                                
                                {% if supplier.credit_limit > 0 %}
                                <div class="mb-3">
                                    <small class="text-muted">الحد الائتماني:</small>
                                    <div class="text-success fw-bold">{{ supplier.credit_limit|floatformat:0 }} ريال</div>
                                </div>
                                {% endif %}
                                
                                <div class="mb-3">
                                    <small class="text-muted">أوامر الشراء:</small>
                                    <div>
                                        <span class="badge bg-primary">{{ supplier.purchase_orders.count }}</span>
                                        <small class="text-muted">أمر</small>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'procurement:supplier_detail' supplier.pk %}" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                        تفاصيل
                                    </a>
                                    <a href="{% url 'procurement:supplier_edit' supplier.pk %}" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                        تعديل
                                    </a>
                                    <a href="{% url 'procurement:evaluate_supplier' supplier.pk %}" 
                                       class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-star"></i>
                                        تقييم
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.city %}&city={{ request.GET.city }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد موردين</h5>
                    <p class="text-muted">
                        {% if request.GET.search or request.GET.city or request.GET.status %}
                        لم يتم العثور على موردين يطابقون معايير البحث.
                        {% else %}
                        لم يتم إضافة أي موردين بعد.
                        {% endif %}
                    </p>
                    {% if not request.GET.search and not request.GET.city and not request.GET.status %}
                    <a href="{% url 'procurement:supplier_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول مورد
                    </a>
                    {% else %}
                    <a href="{% url 'procurement:supplier_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        عرض جميع الموردين
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on Enter
document.getElementById('search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});

// Auto-submit on city/status change
document.getElementById('city').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

// Confirmation for evaluation
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href*="evaluate"]')) {
        if (!confirm('هل تريد تقييم هذا المورد؟')) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
