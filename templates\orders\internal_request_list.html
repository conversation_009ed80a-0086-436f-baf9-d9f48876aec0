{% extends 'base/base.html' %}
{% load static %}

{% block title %}الطلبات الداخلية - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:order_list' %}">الطلبات</a></li>
        <li class="breadcrumb-item active">الطلبات الداخلية</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-building me-2"></i>
                الطلبات الداخلية
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'orders:internal_request_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    طلب داخلي جديد
                </a>
                <a href="{% url 'orders:order_list' %}" class="btn btn-outline-info">
                    <i class="fas fa-file-medical me-2"></i>
                    طلبات صرف الأدوية
                </a>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-file-export me-2"></i>
                        التقارير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#">الطلبات المعلقة</a></li>
                        <li><a class="dropdown-item" href="#">الطلبات المكتملة</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">تصدير Excel</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ requests|length }}</h4>
                <small class="text-muted">إجمالي الطلبات</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with pending_count=0 %}
                        {% for request in requests %}
                            {% if request.status == 'pending' %}
                                {% with pending_count=pending_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ pending_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">في الانتظار</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% with approved_count=0 %}
                        {% for request in requests %}
                            {% if request.status == 'approved' %}
                                {% with approved_count=approved_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ approved_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">موافق عليها</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with fulfilled_count=0 %}
                        {% for request in requests %}
                            {% if request.status == 'fulfilled' %}
                                {% with fulfilled_count=fulfilled_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ fulfilled_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مكتملة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with urgent_count=0 %}
                        {% for request in requests %}
                            {% if request.request_type == 'emergency' %}
                                {% with urgent_count=urgent_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ urgent_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">طوارئ</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-dark">
            <div class="card-body">
                <h4 class="text-dark mb-1">
                    {% with overdue_count=0 %}
                        {% for request in requests %}
                            {% if request.needed_by < today and request.status != 'fulfilled' %}
                                {% with overdue_count=overdue_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ overdue_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">متأخرة</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.GET.search }}" placeholder="رقم الطلب أو اسم القسم...">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>في الانتظار</option>
                            <option value="approved" {% if request.GET.status == 'approved' %}selected{% endif %}>موافق عليه</option>
                            <option value="fulfilled" {% if request.GET.status == 'fulfilled' %}selected{% endif %}>مكتمل</option>
                            <option value="rejected" {% if request.GET.status == 'rejected' %}selected{% endif %}>مرفوض</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="request_type" class="form-label">نوع الطلب</label>
                        <select class="form-select" id="request_type" name="request_type">
                            <option value="">جميع الأنواع</option>
                            <option value="routine" {% if request.GET.request_type == 'routine' %}selected{% endif %}>روتيني</option>
                            <option value="urgent" {% if request.GET.request_type == 'urgent' %}selected{% endif %}>طارئ</option>
                            <option value="emergency" {% if request.GET.request_type == 'emergency' %}selected{% endif %}>طوارئ</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Requests List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الطلبات الداخلية ({{ requests|length }} طلب)
                </h5>
            </div>
            <div class="card-body">
                {% if requests %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>القسم</th>
                                <th>المستودع</th>
                                <th>نوع الطلب</th>
                                <th>الحالة</th>
                                <th>مطلوب بتاريخ</th>
                                <th>تاريخ الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for req in requests %}
                            <tr class="{% if req.request_type == 'emergency' %}table-danger{% elif req.request_type == 'urgent' %}table-warning{% endif %}">
                                <td>
                                    <strong>{{ req.request_number }}</strong>
                                    {% if req.request_type == 'emergency' %}
                                    <span class="badge bg-danger ms-1">طوارئ</span>
                                    {% elif req.request_type == 'urgent' %}
                                    <span class="badge bg-warning text-dark ms-1">طارئ</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ req.department.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ req.department.institution.name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ req.warehouse.name }}</span>
                                </td>
                                <td>
                                    {% if req.request_type == 'emergency' %}
                                    <span class="badge bg-danger">طوارئ</span>
                                    {% elif req.request_type == 'urgent' %}
                                    <span class="badge bg-warning text-dark">طارئ</span>
                                    {% else %}
                                    <span class="badge bg-secondary">روتيني</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if req.status == 'pending' %}
                                    <span class="badge bg-warning text-dark">في الانتظار</span>
                                    {% elif req.status == 'approved' %}
                                    <span class="badge bg-info">موافق عليه</span>
                                    {% elif req.status == 'partially_fulfilled' %}
                                    <span class="badge bg-primary">مكتمل جزئياً</span>
                                    {% elif req.status == 'fulfilled' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif req.status == 'rejected' %}
                                    <span class="badge bg-danger">مرفوض</span>
                                    {% elif req.status == 'cancelled' %}
                                    <span class="badge bg-secondary">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong class="{% if req.needed_by < today and req.status != 'fulfilled' %}text-danger{% endif %}">
                                            {{ req.needed_by|date:"d/m/Y" }}
                                        </strong>
                                        <br>
                                        <small class="text-muted">{{ req.needed_by|time:"H:i" }}</small>
                                        {% if req.needed_by < today and req.status != 'fulfilled' %}
                                        <br><small class="text-danger">متأخر</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ req.created_at|date:"d/m/Y" }}</strong>
                                        <br>
                                        <small class="text-muted">{{ req.created_at|time:"H:i" }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'orders:internal_request_detail' req.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if req.status == 'pending' %}
                                        <a href="{% url 'orders:internal_request_edit' req.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="إجراءات">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item text-success" href="{% url 'orders:approve_internal_request' req.pk %}">
                                                    <i class="fas fa-check me-2"></i>موافقة
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'orders:reject_internal_request' req.pk %}">
                                                    <i class="fas fa-times me-2"></i>رفض
                                                </a></li>
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.request_type %}&request_type={{ request.GET.request_type }}{% endif %}">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.request_type %}&request_type={{ request.GET.request_type }}{% endif %}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.request_type %}&request_type={{ request.GET.request_type }}{% endif %}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.request_type %}&request_type={{ request.GET.request_type }}{% endif %}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات داخلية</h5>
                    <p class="text-muted">
                        {% if request.GET.search or request.GET.status or request.GET.request_type %}
                        لم يتم العثور على طلبات تطابق معايير البحث.
                        {% else %}
                        لم يتم إنشاء أي طلبات داخلية بعد.
                        {% endif %}
                    </p>
                    {% if not request.GET.search and not request.GET.status and not request.GET.request_type %}
                    <a href="{% url 'orders:internal_request_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أول طلب داخلي
                    </a>
                    {% else %}
                    <a href="{% url 'orders:internal_request_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        عرض جميع الطلبات
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on Enter
document.getElementById('search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});

// Auto-submit on status/type change
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('request_type').addEventListener('change', function() {
    this.form.submit();
});

// Confirmation for actions
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href*="approve"]')) {
        if (!confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
            e.preventDefault();
        }
    }
    if (e.target.closest('a[href*="reject"]')) {
        if (!confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
