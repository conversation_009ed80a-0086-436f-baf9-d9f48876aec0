{% extends 'base/base.html' %}
{% load static %}

{% block title %}تقرير حركات المخزون - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">تقرير حركات المخزون</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-exchange-alt text-info me-2"></i>
                تقرير حركات المخزون
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقرير
                </h6>
            </div>
            <div class="card-body">
                <form id="reportFilters" class="row g-3">
                    <div class="col-md-3">
                        <label for="dateFrom" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-3">
                        <label for="dateTo" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="dateTo">
                    </div>
                    <div class="col-md-3">
                        <label for="movementType" class="form-label">نوع الحركة</label>
                        <select class="form-select" id="movementType">
                            <option value="">جميع الحركات</option>
                            <option value="in">إدخال</option>
                            <option value="out">إخراج</option>
                            <option value="transfer">نقل</option>
                            <option value="adjustment">تسوية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-sync me-2"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">1,245</h4>
                <small class="text-muted">إجمالي الحركات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">680</h4>
                <small class="text-muted">حركات إدخال</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">485</h4>
                <small class="text-muted">حركات إخراج</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">80</h4>
                <small class="text-muted">حركات نقل</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    اتجاه حركات المخزون
                </h6>
            </div>
            <div class="card-body">
                <canvas id="movementTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع أنواع الحركات
                </h6>
            </div>
            <div class="card-body">
                <canvas id="movementTypeChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Movement Details Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل حركات المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الحركة</th>
                                <th>نوع الحركة</th>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الكمية</th>
                                <th>التاريخ</th>
                                <th>المستخدم</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>MOV-2024-001</strong></td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-arrow-down me-1"></i>
                                        إدخال
                                    </span>
                                </td>
                                <td>بانادول 500 مجم</td>
                                <td>المستودع الرئيسي</td>
                                <td class="text-success fw-bold">+100</td>
                                <td>2024-12-15 14:30</td>
                                <td>أحمد محمد</td>
                                <td>استلام من المورد</td>
                            </tr>
                            <tr>
                                <td><strong>MOV-2024-002</strong></td>
                                <td>
                                    <span class="badge bg-warning">
                                        <i class="fas fa-arrow-up me-1"></i>
                                        إخراج
                                    </span>
                                </td>
                                <td>أموكسيسيلين 500 مجم</td>
                                <td>المستودع الرئيسي</td>
                                <td class="text-warning fw-bold">-50</td>
                                <td>2024-12-15 10:15</td>
                                <td>فاطمة علي</td>
                                <td>صرف لمستشفى الملك فهد</td>
                            </tr>
                            <tr>
                                <td><strong>MOV-2024-003</strong></td>
                                <td>
                                    <span class="badge bg-info">
                                        <i class="fas fa-exchange-alt me-1"></i>
                                        نقل
                                    </span>
                                </td>
                                <td>جلوكوفاج 850 مجم</td>
                                <td>مستودع الطوارئ</td>
                                <td class="text-info fw-bold">+25</td>
                                <td>2024-12-14 16:45</td>
                                <td>محمد سالم</td>
                                <td>نقل من المستودع الرئيسي</td>
                            </tr>
                            <tr>
                                <td><strong>MOV-2024-004</strong></td>
                                <td>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-balance-scale me-1"></i>
                                        تسوية
                                    </span>
                                </td>
                                <td>أنسولين سريع المفعول</td>
                                <td>المستودع الرئيسي</td>
                                <td class="text-danger fw-bold">-5</td>
                                <td>2024-12-14 09:20</td>
                                <td>سارة أحمد</td>
                                <td>تسوية جرد - منتهي الصلاحية</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Movement Trend Chart
    const trendCtx = document.getElementById('movementTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'حركات إدخال',
                data: [120, 135, 110, 140, 125, 130],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }, {
                label: 'حركات إخراج',
                data: [95, 105, 88, 115, 98, 102],
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4
            }, {
                label: 'حركات نقل',
                data: [15, 18, 12, 20, 16, 14],
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Movement Type Chart
    const typeCtx = document.getElementById('movementTypeChart').getContext('2d');
    new Chart(typeCtx, {
        type: 'doughnut',
        data: {
            labels: ['إدخال', 'إخراج', 'نقل', 'تسوية'],
            datasets: [{
                data: [680, 485, 80, 35],
                backgroundColor: ['#28a745', '#ffc107', '#17a2b8', '#6c757d']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function generateReport() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        showToast('تم تحديث التقرير بنجاح', 'success');
    }, 2000);
}

function exportToExcel() {
    showToast('جاري تصدير التقرير إلى Excel...', 'info');
}

function exportToPDF() {
    showToast('جاري تصدير التقرير إلى PDF...', 'info');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
