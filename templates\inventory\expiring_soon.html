{% extends 'base/base.html' %}
{% load static %}

{% block title %}الأدوية التي تنتهي قريباً - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item active">تنتهي قريباً</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-calendar-alt text-warning me-2"></i>
                الأدوية التي تنتهي قريباً
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:expired_drugs' %}" class="btn btn-danger">
                    <i class="fas fa-clock me-2"></i>
                    المنتهية الصلاحية
                </a>
                <a href="{% url 'inventory:batch_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    جميع الدفعات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Alert Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <h5 class="alert-heading">
                <i class="fas fa-calendar-exclamation me-2"></i>
                تنبيه: أدوية تنتهي خلال 30 يوماً
            </h5>
            <p class="mb-0">
                يوجد <strong>{{ expiring_batches|length }}</strong> دفعة دواء ستنتهي صلاحيتها خلال الـ 30 يوماً القادمة. 
                يُنصح بالتخطيط لاستخدام هذه الأدوية أولاً أو إعادة ترتيب المخزون.
            </p>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">{{ expiring_batches|length }}</h4>
                <small class="text-muted">دفعة تنتهي قريباً</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with critical_count=0 %}
                        {% for batch in expiring_batches %}
                            {% if batch.days_to_expiry <= 7 %}
                                {% with critical_count=critical_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ critical_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">خلال أسبوع</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% regroup expiring_batches by drug as drug_groups %}
                    {{ drug_groups|length }}
                </h4>
                <small class="text-muted">دواء متأثر</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">
                    {% with total_quantity=0 %}
                        {% for batch in expiring_batches %}
                            {% for item in batch.inventory_items.all %}
                                {% with total_quantity=total_quantity|add:item.quantity %}{% endwith %}
                            {% endfor %}
                        {% endfor %}
                        {{ total_quantity }}
                    {% endwith %}
                </h4>
                <small class="text-muted">وحدة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with total_value=0 %}
                        {% for batch in expiring_batches %}
                            {% for item in batch.inventory_items.all %}
                                {% with item_value=batch.cost_price|mul:item.quantity %}
                                    {% with total_value=total_value|add:item_value %}{% endwith %}
                                {% endwith %}
                            {% endfor %}
                        {% endfor %}
                        {{ total_value|floatformat:0 }}
                    {% endwith %}
                </h4>
                <small class="text-muted">ريال قيمة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-secondary">
            <div class="card-body">
                <h4 class="text-secondary mb-1">30</h4>
                <small class="text-muted">يوم فترة التحذير</small>
            </div>
        </div>
    </div>
</div>

<!-- Expiring Batches -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الدفعات التي تنتهي قريباً
                </h5>
            </div>
            <div class="card-body">
                {% if expiring_batches %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الأولوية</th>
                                <th>الدواء</th>
                                <th>رقم الدفعة</th>
                                <th>تاريخ الانتهاء</th>
                                <th>المتبقي</th>
                                <th>الكمية المتاحة</th>
                                <th>سعر التكلفة</th>
                                <th>القيمة الإجمالية</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for batch in expiring_batches %}
                            <tr class="{% if batch.days_to_expiry <= 7 %}table-danger{% elif batch.days_to_expiry <= 14 %}table-warning{% endif %}">
                                <td>
                                    {% if batch.days_to_expiry <= 3 %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        عاجل جداً
                                    </span>
                                    {% elif batch.days_to_expiry <= 7 %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        عاجل
                                    </span>
                                    {% elif batch.days_to_expiry <= 14 %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-info-circle me-1"></i>
                                        متوسط
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-clock me-1"></i>
                                        منخفض
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ batch.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ batch.drug.scientific_name }}</small>
                                        <br>
                                        <span class="badge bg-primary">{{ batch.drug.category.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <code>{{ batch.batch_number }}</code>
                                    {% if batch.supplier_batch_number %}
                                    <br><small class="text-muted">مورد: {{ batch.supplier_batch_number }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="{% if batch.days_to_expiry <= 7 %}text-danger{% elif batch.days_to_expiry <= 14 %}text-warning{% endif %} fw-bold">
                                        {{ batch.expiry_date|date:"d/m/Y" }}
                                    </span>
                                </td>
                                <td>
                                    {% if batch.days_to_expiry <= 3 %}
                                    <span class="badge bg-danger">{{ batch.days_to_expiry }} يوم</span>
                                    {% elif batch.days_to_expiry <= 7 %}
                                    <span class="badge bg-warning text-dark">{{ batch.days_to_expiry }} يوم</span>
                                    {% elif batch.days_to_expiry <= 14 %}
                                    <span class="badge bg-info">{{ batch.days_to_expiry }} يوم</span>
                                    {% else %}
                                    <span class="text-secondary">{{ batch.days_to_expiry }} يوم</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% with total_qty=0 %}
                                        {% for item in batch.inventory_items.all %}
                                            {% with total_qty=total_qty|add:item.quantity %}{% endwith %}
                                        {% endfor %}
                                        <span class="fw-bold">{{ total_qty }}</span>
                                        <small class="text-muted">{{ batch.drug.get_unit_display }}</small>
                                        {% if batch.inventory_items.count > 1 %}
                                        <br><small class="text-muted">في {{ batch.inventory_items.count }} مستودع</small>
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td class="text-success">{{ batch.cost_price }} ريال</td>
                                <td>
                                    {% with total_qty=0 %}
                                        {% for item in batch.inventory_items.all %}
                                            {% with total_qty=total_qty|add:item.quantity %}{% endwith %}
                                        {% endfor %}
                                        {% with value=batch.cost_price|mul:total_qty %}
                                        <span class="text-success fw-bold">{{ value|floatformat:2 }} ريال</span>
                                        {% endwith %}
                                    {% endwith %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-warning" 
                                                onclick="prioritizeUsage({{ batch.pk }})" title="أولوية الاستخدام">
                                            <i class="fas fa-star"></i>
                                        </button>
                                        <a href="{% url 'inventory:batch_detail' batch.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="createDispenseOrder({{ batch.pk }})" title="إنشاء أمر صرف">
                                            <i class="fas fa-prescription"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" 
                                                onclick="showBatchLocations({{ batch.pk }})" title="عرض المواقع">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">إجراءات سريعة:</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-warning" onclick="prioritizeAllExpiring()">
                                        <i class="fas fa-star me-2"></i>
                                        أولوية استخدام الكل
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="createBulkDispenseOrders()">
                                        <i class="fas fa-prescription me-2"></i>
                                        أوامر صرف جماعية
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="generateExpiryReport()">
                                        <i class="fas fa-file-alt me-2"></i>
                                        تقرير انتهاء الصلاحية
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="exportExpiringReport()">
                                        <i class="fas fa-file-export me-2"></i>
                                        تصدير التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد أدوية تنتهي قريباً</h5>
                    <p class="text-muted">جميع دفعات الأدوية في المخزون لديها فترة صلاحية كافية (أكثر من 30 يوماً).</p>
                    <a href="{% url 'inventory:batch_list' %}" class="btn btn-primary">
                        <i class="fas fa-boxes me-2"></i>
                        عرض جميع الدفعات
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Usage Recommendations -->
{% if expiring_batches %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    توصيات الاستخدام الأمثل
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>استراتيجيات الاستخدام:</h6>
                        <ul class="small">
                            <li><strong>FEFO (First Expired, First Out):</strong> استخدم الأدوية الأقرب لانتهاء الصلاحية أولاً</li>
                            <li><strong>أولوية الصرف:</strong> أعط أولوية لصرف الدفعات التي تنتهي قريباً</li>
                            <li><strong>إعادة التوزيع:</strong> انقل الأدوية إلى المستودعات الأكثر استخداماً</li>
                            <li><strong>العروض الخاصة:</strong> قدم خصومات للأدوية القريبة من الانتهاء</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>إجراءات وقائية:</h6>
                        <ul class="small">
                            <li>مراجعة دورية لتواريخ انتهاء الصلاحية</li>
                            <li>تحسين عمليات الشراء والتخزين</li>
                            <li>تدريب الموظفين على إدارة المخزون</li>
                            <li>استخدام أنظمة التنبيه المبكر</li>
                            <li>التنسيق مع الأقسام الطبية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function prioritizeUsage(batchId) {
    alert('سيتم تطوير هذه الميزة قريباً - تحديد أولوية استخدام الدفعة');
}

function createDispenseOrder(batchId) {
    alert('سيتم تطوير هذه الميزة قريباً - إنشاء أمر صرف للدفعة');
}

function showBatchLocations(batchId) {
    alert('سيتم تطوير هذه الميزة قريباً - عرض مواقع الدفعة في المستودعات');
}

function prioritizeAllExpiring() {
    alert('سيتم تطوير هذه الميزة قريباً - تحديد أولوية استخدام جميع الدفعات المنتهية قريباً');
}

function createBulkDispenseOrders() {
    alert('سيتم تطوير هذه الميزة قريباً - إنشاء أوامر صرف جماعية');
}

function generateExpiryReport() {
    alert('سيتم تطوير هذه الميزة قريباً - إنشاء تقرير انتهاء الصلاحية');
}

function exportExpiringReport() {
    alert('سيتم تطوير هذه الميزة قريباً - تصدير تقرير الأدوية التي تنتهي قريباً');
}
</script>
{% endblock %}
