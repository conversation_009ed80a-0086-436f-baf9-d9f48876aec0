{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ category.name }} - تفاصيل التصنيف{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:category_list' %}">تصنيفات الأدوية</a></li>
        <li class="breadcrumb-item active">{{ category.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>
                {{ category.name }}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'drugs:category_edit' category.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <a href="{% url 'drugs:category_delete' category.pk %}" class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من حذف هذا التصنيف؟')">
                    <i class="fas fa-trash me-2"></i>
                    حذف
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Category Information -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات التصنيف
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-tags"></i>
                    </div>
                    <h4 class="text-primary">{{ category.name }}</h4>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted small">الوصف</label>
                    {% if category.description %}
                    <p class="mb-0">{{ category.description }}</p>
                    {% else %}
                    <p class="text-muted mb-0">لا يوجد وصف لهذا التصنيف</p>
                    {% endif %}
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary mb-1">{{ category.drug_set.count }}</h4>
                        <small class="text-muted">دواء</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ category.drug_set.filter.is_active.count|default:category.drug_set.count }}</h4>
                        <small class="text-muted">نشط</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <div class="d-flex justify-content-between mb-2">
                        <span>تاريخ الإنشاء:</span>
                        <span>{{ category.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>رقم التصنيف:</span>
                        <span><code>#{{ category.id }}</code></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'drugs:drug_create' %}?category={{ category.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دواء لهذا التصنيف
                    </a>
                    <a href="{% url 'drugs:drug_list' %}?category={{ category.id }}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>
                        عرض جميع الأدوية
                    </a>
                    <a href="{% url 'drugs:category_edit' category.pk %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل التصنيف
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Drugs in this Category -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pills me-2"></i>
                    الأدوية في هذا التصنيف ({{ category.drug_set.count }})
                </h5>
                {% if category.drug_set.exists %}
                <a href="{% url 'drugs:drug_list' %}?category={{ category.id }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if category.drug_set.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم التجاري</th>
                                <th>الاسم العلمي</th>
                                <th>الشركة المصنعة</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for drug in category.drug_set.all|slice:":10" %}
                            <tr>
                                <td>
                                    <strong>{{ drug.commercial_name }}</strong>
                                    {% if drug.is_controlled %}
                                    <span class="badge bg-warning text-dark ms-1">مراقب</span>
                                    {% endif %}
                                </td>
                                <td>{{ drug.scientific_name }}</td>
                                <td>{{ drug.manufacturer.name }}</td>
                                <td class="text-success fw-bold">{{ drug.unit_price }} ريال</td>
                                <td>
                                    {% if drug.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'drugs:drug_detail' drug.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'drugs:drug_edit' drug.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if category.drug_set.count > 10 %}
                <div class="text-center mt-3">
                    <a href="{% url 'drugs:drug_list' %}?category={{ category.id }}" class="btn btn-primary">
                        عرض جميع الأدوية ({{ category.drug_set.count }})
                    </a>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية في هذا التصنيف</h5>
                    <p class="text-muted">لم يتم إضافة أي أدوية لهذا التصنيف بعد.</p>
                    <a href="{% url 'drugs:drug_create' %}?category={{ category.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دواء
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Statistics -->
        {% if category.drug_set.exists %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات التصنيف
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-primary mb-1">{{ category.drug_set.count }}</h5>
                            <small class="text-muted">إجمالي الأدوية</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-success mb-1">{{ category.drug_set.filter.is_active.count|default:category.drug_set.count }}</h5>
                            <small class="text-muted">أدوية نشطة</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-warning mb-1">{{ category.drug_set.filter.is_controlled.count|default:0 }}</h5>
                            <small class="text-muted">أدوية مراقبة</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-info mb-1">
                                {% with manufacturers=category.drug_set.values_list.manufacturer.distinct %}
                                {{ manufacturers|length }}
                                {% endwith %}
                            </h5>
                            <small class="text-muted">شركة مصنعة</small>
                        </div>
                    </div>
                </div>
                
                <!-- Price Range -->
                <div class="mt-3">
                    <h6 class="text-muted">نطاق الأسعار:</h6>
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">أقل سعر:</small>
                            <span class="fw-bold text-success">
                                {% with min_price=category.drug_set.aggregate.unit_price__min %}
                                {{ min_price|default:"غير محدد" }}
                                {% endwith %}
                                ريال
                            </span>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">أعلى سعر:</small>
                            <span class="fw-bold text-danger">
                                {% with max_price=category.drug_set.aggregate.unit_price__max %}
                                {{ max_price|default:"غير محدد" }}
                                {% endwith %}
                                ريال
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
