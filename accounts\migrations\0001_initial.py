# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('institutions', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الصلاحية')),
                ('codename', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'الصلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الدور')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('permissions', models.ManyToManyField(blank=True, to='accounts.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'الدور',
                'verbose_name_plural': 'الأدوار',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'مدير النظام'), ('pharmacist', 'صيدلي'), ('warehouse_manager', 'مدير مستودع'), ('department_head', 'رئيس قسم'), ('procurement_officer', 'مسؤول مشتريات'), ('inventory_clerk', 'موظف مخزون'), ('viewer', 'مشاهد')], max_length=20, verbose_name='الدور')),
                ('employee_id', models.CharField(max_length=50, unique=True, verbose_name='رقم الموظف')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='الهاتف')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='الصورة الشخصية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='institutions.department', verbose_name='القسم')),
                ('institution', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='institutions.institution', verbose_name='المؤسسة')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف المستخدم',
                'verbose_name_plural': 'ملفات المستخدمين',
                'ordering': ['user__first_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('assigned_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='assigned_roles', to=settings.AUTH_USER_MODEL, verbose_name='عين بواسطة')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='institutions.department', verbose_name='القسم')),
                ('institution', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='institutions.institution', verbose_name='المؤسسة')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.role', verbose_name='الدور')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دور المستخدم',
                'verbose_name_plural': 'أدوار المستخدمين',
                'unique_together': {('user', 'role', 'institution', 'department')},
            },
        ),
    ]
