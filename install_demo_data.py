#!/usr/bin/env python
"""
Script to install comprehensive demo data for the Drug Management System
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def setup_django():
    """Setup Django environment"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dms_project.settings')
    django.setup()

def run_command(command):
    """Run a Django management command"""
    print(f"\n{'='*60}")
    print(f"تشغيل الأمر: {command}")
    print(f"{'='*60}")
    
    try:
        execute_from_command_line(['manage.py'] + command.split())
        print(f"✅ تم تنفيذ الأمر بنجاح: {command}")
    except Exception as e:
        print(f"❌ خطأ في تنفيذ الأمر {command}: {str(e)}")
        return False
    return True

def main():
    """Main function to install demo data"""
    print("🚀 بدء تثبيت البيانات التجريبية لنظام إدارة الأدوية")
    print("="*60)
    
    # Setup Django
    setup_django()
    
    # List of commands to run in order
    commands = [
        "makemigrations",
        "migrate",
        "generate_demo_data --clear",
        "generate_advanced_demo",
        "generate_reports_data",
    ]
    
    success_count = 0
    total_commands = len(commands)
    
    for command in commands:
        if run_command(command):
            success_count += 1
        else:
            print(f"\n❌ فشل في تنفيذ الأمر: {command}")
            print("توقف التثبيت بسبب خطأ.")
            sys.exit(1)
    
    print(f"\n{'='*60}")
    print("🎉 تم تثبيت البيانات التجريبية بنجاح!")
    print(f"{'='*60}")
    print(f"✅ تم تنفيذ {success_count}/{total_commands} أوامر بنجاح")
    
    print("\n📊 ملخص البيانات المثبتة:")
    print("-" * 40)
    
    # Import models after Django setup
    from drugs.models import Drug, DrugCategory, Manufacturer
    from inventory.models import Warehouse, Batch, InventoryItem, InventoryMovement
    from orders.models import Order, OrderItem, InternalRequest
    from procurement.models import Supplier, PurchaseOrder
    from accounts.models import Notification
    from django.contrib.auth.models import User
    
    try:
        print(f"👥 المستخدمين: {User.objects.count()}")
        print(f"🏷️  تصنيفات الأدوية: {DrugCategory.objects.count()}")
        print(f"🏭 الشركات المصنعة: {Manufacturer.objects.count()}")
        print(f"💊 الأدوية: {Drug.objects.count()}")
        print(f"🏢 المستودعات: {Warehouse.objects.count()}")
        print(f"📦 الدفعات: {Batch.objects.count()}")
        print(f"📋 عناصر المخزون: {InventoryItem.objects.count()}")
        print(f"🔄 حركات المخزون: {InventoryMovement.objects.count()}")
        print(f"📝 الطلبات: {Order.objects.count()}")
        print(f"📄 عناصر الطلبات: {OrderItem.objects.count()}")
        print(f"🔄 الطلبات الداخلية: {InternalRequest.objects.count()}")
        print(f"🚚 الموردين: {Supplier.objects.count()}")
        print(f"🛒 أوامر الشراء: {PurchaseOrder.objects.count()}")
        print(f"🔔 التنبيهات: {Notification.objects.count()}")
        
        print(f"\n{'='*60}")
        print("🎯 معلومات تسجيل الدخول:")
        print("-" * 30)
        print("المستخدم: admin")
        print("كلمة المرور: admin123")
        print("\nمستخدمين إضافيين:")
        print("- pharmacist1 / password123")
        print("- pharmacist2 / password123")
        print("- manager1 / password123")
        print("- nurse1 / password123")
        print("- doctor1 / password123")
        
        print(f"\n{'='*60}")
        print("🌐 روابط مهمة:")
        print("-" * 20)
        print("🏠 لوحة التحكم: http://127.0.0.1:8000/")
        print("💊 الأدوية: http://127.0.0.1:8000/drugs/")
        print("📦 المخزون: http://127.0.0.1:8000/inventory/")
        print("📋 الطلبات: http://127.0.0.1:8000/orders/")
        print("🛒 المشتريات: http://127.0.0.1:8000/procurement/")
        print("📊 التقارير: http://127.0.0.1:8000/reports/")
        print("👤 الحساب: http://127.0.0.1:8000/accounts/profile/")
        
        print(f"\n{'='*60}")
        print("📈 تقارير متاحة:")
        print("-" * 25)
        print("📊 تقرير المخزون الشامل")
        print("⚠️  تقرير المخزون المنخفض")
        print("⏰ تقرير انتهاء الصلاحية")
        print("🔄 تقرير حركات المخزون")
        print("📋 تقرير الطلبات")
        print("💰 التقرير المالي")
        print("🚚 تقرير الموردين")
        print("📈 تقرير الاستخدام")
        print("🎯 تقارير مخصصة")
        
        print(f"\n{'='*60}")
        print("🎉 النظام جاهز للاستخدام!")
        print("يمكنك الآن تشغيل الخادم باستخدام:")
        print("python manage.py runserver")
        print(f"{'='*60}")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {str(e)}")

if __name__ == "__main__":
    main()
