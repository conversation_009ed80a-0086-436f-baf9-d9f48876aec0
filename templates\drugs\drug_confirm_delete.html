{% extends 'base/base.html' %}
{% load static %}

{% block title %}حذف الدواء: {{ drug.commercial_name }} - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:drug_list' %}">قائمة الأدوية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:drug_detail' drug.pk %}">{{ drug.commercial_name }}</a></li>
        <li class="breadcrumb-item active">حذف الدواء</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-warning me-2"></i>
                        تحذير مهم!
                    </h6>
                    <p class="mb-0">
                        أنت على وشك حذف الدواء نهائياً. هذا الإجراء لا يمكن التراجع عنه وقد يؤثر على:
                    </p>
                    <ul class="mt-2 mb-0">
                        <li>الطلبات المرتبطة بهذا الدواء</li>
                        <li>بيانات المخزون</li>
                        <li>التقارير والإحصائيات</li>
                        <li>سجل الحركات</li>
                    </ul>
                </div>

                <!-- Drug Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">معلومات الدواء المراد حذفه</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">الاسم التجاري</label>
                                <p class="fw-bold">{{ drug.commercial_name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">الاسم العلمي</label>
                                <p class="fw-bold">{{ drug.scientific_name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">التصنيف</label>
                                <p>{{ drug.category.name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">الشركة المصنعة</label>
                                <p>{{ drug.manufacturer.name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">سعر الوحدة</label>
                                <p class="text-success fw-bold">{{ drug.unit_price }} ريال</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">الحالة</label>
                                <p>
                                    {% if drug.is_active %}
                                    <span class="badge bg-success">نشط</span>
                                    {% else %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                    {% if drug.is_controlled %}
                                    <span class="badge bg-warning text-dark ms-1">مراقب</span>
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Impact Assessment -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">تقييم التأثير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-primary mb-1">{{ drug.inventory_items.count }}</h5>
                                    <small class="text-muted">عنصر مخزون</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-warning mb-1">0</h5>
                                    <small class="text-muted">طلب نشط</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-info mb-1">0</h5>
                                    <small class="text-muted">أمر شراء</small>
                                </div>
                            </div>
                            <div class="col-md-3 text-center mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-success mb-1">0</h5>
                                    <small class="text-muted">حركة مخزون</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if drug.inventory_items.exists %}
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> يوجد {{ drug.inventory_items.count }} عنصر مخزون مرتبط بهذا الدواء. 
                            حذف الدواء سيؤدي إلى حذف جميع بيانات المخزون المرتبطة به.
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Confirmation Form -->
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                        <label class="form-check-label" for="confirmDelete">
                            <strong>أؤكد أنني أفهم عواقب هذا الإجراء وأريد المتابعة</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'drugs:drug_detail' drug.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                            <i class="fas fa-trash me-2"></i>
                            حذف الدواء نهائياً
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
        
        if (this.checked) {
            deleteButton.classList.remove('btn-secondary');
            deleteButton.classList.add('btn-danger');
        } else {
            deleteButton.classList.remove('btn-danger');
            deleteButton.classList.add('btn-secondary');
        }
    });
    
    // Additional confirmation on form submit
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!confirm('هل أنت متأكد تماماً من حذف هذا الدواء؟ لا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
