{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل الشركة المصنعة{% else %}إضافة شركة مصنعة جديدة{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:manufacturer_list' %}">الشركات المصنعة</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل الشركة المصنعة{% else %}إضافة شركة مصنعة جديدة{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-industry me-2"></i>
                    {% if object %}تعديل الشركة المصنعة: {{ object.name }}{% else %}إضافة شركة مصنعة جديدة{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">المعلومات الأساسية</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                اسم الشركة <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.country.id_for_label }}" class="form-label">
                                البلد <span class="text-danger">*</span>
                            </label>
                            {{ form.country }}
                            {% if form.country.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.country.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.contact_info.id_for_label }}" class="form-label">
                                معلومات الاتصال
                            </label>
                            {{ form.contact_info }}
                            {% if form.contact_info.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.contact_info.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أدخل معلومات الاتصال مثل العنوان، الهاتف، البريد الإلكتروني، إلخ
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">الحالة</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    شركة نشطة
                                </label>
                                <div class="form-text">
                                    الشركات النشطة فقط تظهر في قوائم الاختيار عند إضافة الأدوية
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'drugs:manufacturer_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}تحديث الشركة{% else %}إضافة الشركة{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">إرشادات إضافة الشركات:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من صحة اسم الشركة</li>
                        <li>استخدم الاسم الرسمي للشركة</li>
                        <li>أدخل البلد الأصلي للشركة</li>
                        <li>أضف معلومات اتصال شاملة</li>
                        <li>تحقق من عدم وجود الشركة مسبقاً</li>
                    </ul>
                </div>
                
                {% if object %}
                <div class="alert alert-warning">
                    <h6 class="alert-heading">تحذير:</h6>
                    <p class="mb-0 small">
                        تعديل معلومات الشركة قد يؤثر على الأدوية المرتبطة بها.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Related Drugs (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-pills me-2"></i>
                    الأدوية المصنعة ({{ object.drug_set.count }})
                </h6>
            </div>
            <div class="card-body">
                {% if object.drug_set.exists %}
                <div class="list-group list-group-flush">
                    {% for drug in object.drug_set.all|slice:":5" %}
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-pill text-primary me-2"></i>
                            <div>
                                <h6 class="mb-0 small">{{ drug.commercial_name }}</h6>
                                <small class="text-muted">{{ drug.scientific_name }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if object.drug_set.count > 5 %}
                <div class="text-center mt-3">
                    <a href="{% url 'drugs:drug_list' %}?manufacturer={{ object.id }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الأدوية ({{ object.drug_set.count }})
                    </a>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-info-circle text-muted me-2"></i>
                    <span class="text-muted small">لا توجد أدوية مرتبطة بهذه الشركة</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Statistics (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary mb-1">{{ object.drug_set.count }}</h4>
                        <small class="text-muted">دواء</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">
                            {% if object.is_active %}
                            <i class="fas fa-check-circle"></i>
                            {% else %}
                            <i class="fas fa-times-circle text-danger"></i>
                            {% endif %}
                        </h4>
                        <small class="text-muted">الحالة</small>
                    </div>
                </div>
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        أضيفت في {{ object.created_at|date:"d/m/Y" }}
                    </small>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on the name field
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    if (nameField) {
        nameField.focus();
    }
    
    // Country suggestions
    const countryField = document.getElementById('{{ form.country.id_for_label }}');
    if (countryField) {
        const commonCountries = [
            'السعودية', 'الولايات المتحدة', 'ألمانيا', 'سويسرا', 'فرنسا', 
            'المملكة المتحدة', 'إيطاليا', 'اليابان', 'الهند', 'كندا'
        ];
        
        // Add datalist for country suggestions
        const datalist = document.createElement('datalist');
        datalist.id = 'country-suggestions';
        commonCountries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            datalist.appendChild(option);
        });
        
        countryField.setAttribute('list', 'country-suggestions');
        countryField.parentNode.appendChild(datalist);
    }
});
</script>
{% endblock %}
