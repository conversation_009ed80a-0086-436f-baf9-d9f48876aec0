{% extends 'base/base.html' %}
{% load static %}

{% block title %}تصنيفات الأدوية - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">تصنيفات الأدوية</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>
                تصنيفات الأدوية
            </h1>
            <a href="{% url 'drugs:category_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة تصنيف جديد
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة التصنيفات ({{ categories|length }} تصنيف)
                </h5>
            </div>
            <div class="card-body">
                {% if categories %}
                <div class="row">
                    {% for category in categories %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 border-start border-primary border-4">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title text-primary">{{ category.name }}</h5>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{% url 'drugs:category_detail' category.pk %}">
                                                <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                            </a></li>
                                            <li><a class="dropdown-item" href="{% url 'drugs:category_edit' category.pk %}">
                                                <i class="fas fa-edit me-2"></i>تعديل
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="{% url 'drugs:category_delete' category.pk %}">
                                                <i class="fas fa-trash me-2"></i>حذف
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                {% if category.description %}
                                <p class="card-text text-muted">{{ category.description|truncatewords:15 }}</p>
                                {% else %}
                                <p class="card-text text-muted">لا يوجد وصف</p>
                                {% endif %}
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-pills me-1"></i>
                                        {{ category.drug_set.count }} دواء
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ category.created_at|date:"d/m/Y" }}
                                    </small>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'drugs:category_detail' category.pk %}" class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'drugs:category_edit' category.pk %}" class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'drugs:drug_list' %}?category={{ category.id }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-pills"></i> الأدوية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تصنيفات</h5>
                    <p class="text-muted">لم يتم إضافة أي تصنيفات للأدوية بعد.</p>
                    <a href="{% url 'drugs:category_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول تصنيف
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
