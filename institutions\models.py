from django.db import models
from django.contrib.auth.models import User

class Institution(models.Model):
    """المؤسسات الطبية (مستشفيات، مراكز صحية)"""
    INSTITUTION_TYPES = [
        ('hospital', 'مستشفى'),
        ('clinic', 'عيادة'),
        ('health_center', 'مركز صحي'),
        ('pharmacy', 'صيدلية'),
        ('laboratory', 'مختبر'),
    ]

    name = models.CharField(max_length=200, verbose_name="اسم المؤسسة")
    institution_type = models.CharField(max_length=20, choices=INSTITUTION_TYPES, verbose_name="نوع المؤسسة")
    license_number = models.CharField(max_length=100, unique=True, verbose_name="رقم الترخيص")

    # معلومات الاتصال
    address = models.TextField(verbose_name="العنوان")
    city = models.CharField(max_length=100, verbose_name="المدينة")
    phone = models.CharField(max_length=20, verbose_name="الهاتف")
    email = models.EmailField(blank=True, verbose_name="البريد الإلكتروني")

    # معلومات إضافية
    contact_person = models.CharField(max_length=200, verbose_name="الشخص المسؤول")
    contact_person_phone = models.CharField(max_length=20, verbose_name="هاتف الشخص المسؤول")

    # حالة المؤسسة
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    credit_limit = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="الحد الائتماني")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "المؤسسة الطبية"
        verbose_name_plural = "المؤسسات الطبية"
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_institution_type_display()})"

class Department(models.Model):
    """الأقسام داخل المؤسسات"""
    institution = models.ForeignKey(Institution, on_delete=models.CASCADE, related_name='departments', verbose_name="المؤسسة")
    name = models.CharField(max_length=200, verbose_name="اسم القسم")
    code = models.CharField(max_length=20, verbose_name="رمز القسم")

    # معلومات القسم
    head_of_department = models.CharField(max_length=200, blank=True, verbose_name="رئيس القسم")
    phone = models.CharField(max_length=20, blank=True, verbose_name="هاتف القسم")
    location = models.CharField(max_length=200, blank=True, verbose_name="الموقع")

    # حالة القسم
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    can_request_drugs = models.BooleanField(default=True, verbose_name="يمكنه طلب الأدوية")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "القسم"
        verbose_name_plural = "الأقسام"
        ordering = ['institution', 'name']
        unique_together = ['institution', 'code']

    def __str__(self):
        return f"{self.institution.name} - {self.name}"
