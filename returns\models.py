from django.db import models
from django.contrib.auth.models import User
from drugs.models import Drug
from institutions.models import Department
from inventory.models import Warehouse, InventoryItem, DrugBatch

class DrugReturn(models.Model):
    """مرتجعات الأدوية"""
    RETURN_TYPE = [
        ('expired', 'منتهي الصلاحية'),
        ('damaged', 'تالف'),
        ('recalled', 'مسحوب'),
        ('excess', 'فائض'),
        ('wrong_item', 'صنف خاطئ'),
        ('quality_issue', 'مشكلة في الجودة'),
    ]

    RETURN_STATUS = [
        ('pending', 'في الانتظار'),
        ('approved', 'موافق عليه'),
        ('processed', 'تم المعالجة'),
        ('rejected', 'مرفوض'),
    ]

    # معلومات أساسية
    return_number = models.CharField(max_length=50, unique=True, verbose_name="رقم المرتجع")
    return_type = models.CharField(max_length=20, choices=RETURN_TYPE, verbose_name="نوع المرتجع")
    status = models.CharField(max_length=20, choices=RETURN_STATUS, default='pending', verbose_name="حالة المرتجع")

    # معلومات المصدر
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True, verbose_name="القسم")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="المستودع")

    # معلومات إضافية
    reason = models.TextField(verbose_name="السبب")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات المستخدم
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='created_returns', verbose_name="أنشأ بواسطة")
    approved_by = models.ForeignKey(User, on_delete=models.PROTECT, null=True, blank=True, related_name='approved_returns', verbose_name="وافق عليه")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")

    class Meta:
        verbose_name = "مرتجع الأدوية"
        verbose_name_plural = "مرتجعات الأدوية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.return_number} - {self.get_return_type_display()}"

    def generate_return_number(self):
        """توليد رقم المرتجع"""
        from django.utils import timezone
        today = timezone.now().date()
        count = DrugReturn.objects.filter(created_at__date=today).count() + 1
        return f"RET-{today.strftime('%Y%m%d')}-{count:04d}"

    def save(self, *args, **kwargs):
        if not self.return_number:
            self.return_number = self.generate_return_number()
        super().save(*args, **kwargs)

class DrugReturnItem(models.Model):
    """عناصر مرتجع الأدوية"""
    drug_return = models.ForeignKey(DrugReturn, on_delete=models.CASCADE, related_name='items', verbose_name="المرتجع")
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, verbose_name="الدواء")
    batch = models.ForeignKey(DrugBatch, on_delete=models.CASCADE, verbose_name="الدفعة")

    # الكميات
    quantity = models.PositiveIntegerField(verbose_name="الكمية")

    # معلومات إضافية
    condition_notes = models.TextField(blank=True, verbose_name="ملاحظات الحالة")

    class Meta:
        verbose_name = "عنصر مرتجع الأدوية"
        verbose_name_plural = "عناصر مرتجع الأدوية"
        unique_together = ['drug_return', 'drug', 'batch']

    def __str__(self):
        return f"{self.drug_return.return_number} - {self.drug.commercial_name}"

class DamagedDrug(models.Model):
    """الأدوية التالفة"""
    DAMAGE_TYPE = [
        ('expired', 'منتهي الصلاحية'),
        ('physical_damage', 'تلف مادي'),
        ('contamination', 'تلوث'),
        ('temperature_damage', 'تلف حراري'),
        ('moisture_damage', 'تلف رطوبة'),
        ('other', 'أخرى'),
    ]

    # معلومات أساسية
    damage_number = models.CharField(max_length=50, unique=True, verbose_name="رقم التلف")
    inventory_item = models.ForeignKey(InventoryItem, on_delete=models.CASCADE, verbose_name="عنصر المخزون")
    damage_type = models.CharField(max_length=20, choices=DAMAGE_TYPE, verbose_name="نوع التلف")

    # تفاصيل التلف
    quantity_damaged = models.PositiveIntegerField(verbose_name="الكمية التالفة")
    damage_description = models.TextField(verbose_name="وصف التلف")
    discovery_date = models.DateTimeField(verbose_name="تاريخ اكتشاف التلف")

    # معلومات إضافية
    estimated_loss = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الخسارة المقدرة")
    disposal_method = models.CharField(max_length=200, blank=True, verbose_name="طريقة التخلص")
    disposal_date = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ التخلص")

    # معلومات المستخدم
    reported_by = models.ForeignKey(User, on_delete=models.PROTECT, verbose_name="أبلغ بواسطة")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "الدواء التالف"
        verbose_name_plural = "الأدوية التالفة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.damage_number} - {self.inventory_item.drug.commercial_name}"

    def generate_damage_number(self):
        """توليد رقم التلف"""
        from django.utils import timezone
        today = timezone.now().date()
        count = DamagedDrug.objects.filter(created_at__date=today).count() + 1
        return f"DMG-{today.strftime('%Y%m%d')}-{count:04d}"

    def save(self, *args, **kwargs):
        if not self.damage_number:
            self.damage_number = self.generate_damage_number()
        super().save(*args, **kwargs)
