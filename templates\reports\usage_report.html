{% extends 'base/base.html' %}
{% load static %}

{% block title %}تقرير الاستخدام العام - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">تقرير الاستخدام العام</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-line text-primary me-2"></i>
                تقرير الاستخدام العام
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقرير
                </h6>
            </div>
            <div class="card-body">
                <form id="reportFilters" class="row g-3">
                    <div class="col-md-3">
                        <label for="period" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="period" name="period">
                            <option value="last_month">الشهر الماضي</option>
                            <option value="last_3_months">آخر 3 أشهر</option>
                            <option value="last_6_months">آخر 6 أشهر</option>
                            <option value="last_year">السنة الماضية</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="institution" class="form-label">المؤسسة</label>
                        <select class="form-select" id="institution" name="institution">
                            <option value="">جميع المؤسسات</option>
                            <option value="1">مستشفى الملك فهد</option>
                            <option value="2">مركز الرعاية الأولية</option>
                            <option value="3">مستشفى الأطفال</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="category" class="form-label">تصنيف الدواء</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع التصنيفات</option>
                            <option value="1">مضادات حيوية</option>
                            <option value="2">مسكنات</option>
                            <option value="3">أدوية القلب</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-sync me-2"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">2,450</h4>
                <small class="text-muted">إجمالي الطلبات</small>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        +12% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">185,000</h4>
                <small class="text-muted">إجمالي الوحدات المصروفة</small>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        +8% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">3,250,000</h4>
                <small class="text-muted">القيمة الإجمالية (ريال)</small>
                <div class="mt-2">
                    <small class="text-success">
                        <i class="fas fa-arrow-up me-1"></i>
                        +15% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">75.5</h4>
                <small class="text-muted">متوسط الوحدات/طلب</small>
                <div class="mt-2">
                    <small class="text-danger">
                        <i class="fas fa-arrow-down me-1"></i>
                        -3% من الشهر الماضي
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    اتجاه الاستخدام الشهري
                </h6>
            </div>
            <div class="card-body">
                <canvas id="usageTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الاستخدام حسب التصنيف
                </h6>
            </div>
            <div class="card-body">
                <canvas id="categoryUsageChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Top Used Drugs -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    أكثر الأدوية استخداماً
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">بانادول 500 مجم</h6>
                            <small class="text-muted">مسكن وخافض حرارة</small>
                        </div>
                        <div class="text-end">
                            <span class="text-primary fw-bold">15,250 وحدة</span>
                            <br>
                            <small class="text-muted">18.5% من الإجمالي</small>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">أموكسيسيلين 500 مجم</h6>
                            <small class="text-muted">مضاد حيوي</small>
                        </div>
                        <div class="text-end">
                            <span class="text-primary fw-bold">12,800 وحدة</span>
                            <br>
                            <small class="text-muted">15.5% من الإجمالي</small>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">جلوكوفاج 850 مجم</h6>
                            <small class="text-muted">أدوية السكري</small>
                        </div>
                        <div class="text-end">
                            <span class="text-primary fw-bold">9,650 وحدة</span>
                            <br>
                            <small class="text-muted">11.7% من الإجمالي</small>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">نورفاسك 5 مجم</h6>
                            <small class="text-muted">أدوية الضغط</small>
                        </div>
                        <div class="text-end">
                            <span class="text-primary fw-bold">8,420 وحدة</span>
                            <br>
                            <small class="text-muted">10.2% من الإجمالي</small>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">أسبرين 100 مجم</h6>
                            <small class="text-muted">مضاد تجلط</small>
                        </div>
                        <div class="text-end">
                            <span class="text-primary fw-bold">7,890 وحدة</span>
                            <br>
                            <small class="text-muted">9.6% من الإجمالي</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    الاستخدام حسب المؤسسة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المؤسسة</th>
                                <th>عدد الطلبات</th>
                                <th>الوحدات المصروفة</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div>
                                        <strong>مستشفى الملك فهد</strong>
                                        <br>
                                        <small class="text-muted">مستشفى عام</small>
                                    </div>
                                </td>
                                <td><span class="fw-bold text-primary">1,250</span></td>
                                <td><span class="text-success">95,000</span></td>
                                <td><span class="text-info">1,850,000 ريال</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <strong>مركز الرعاية الأولية</strong>
                                        <br>
                                        <small class="text-muted">رعاية أولية</small>
                                    </div>
                                </td>
                                <td><span class="fw-bold text-primary">850</span></td>
                                <td><span class="text-success">65,000</span></td>
                                <td><span class="text-info">980,000 ريال</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <strong>مستشفى الأطفال</strong>
                                        <br>
                                        <small class="text-muted">تخصصي أطفال</small>
                                    </div>
                                </td>
                                <td><span class="fw-bold text-primary">350</span></td>
                                <td><span class="text-success">25,000</span></td>
                                <td><span class="text-info">420,000 ريال</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Usage Patterns -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    أنماط الاستخدام اليومية والأسبوعية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <h6 class="text-muted mb-3">الاستخدام حسب أيام الأسبوع</h6>
                        <canvas id="weeklyPatternChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-lg-6">
                        <h6 class="text-muted mb-3">الاستخدام حسب ساعات اليوم</h6>
                        <canvas id="hourlyPatternChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Usage Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل الاستخدام
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="toggleView('summary')">
                        ملخص
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="toggleView('detailed')">
                        تفصيلي
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="usageTable">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>التصنيف</th>
                                <th>عدد الطلبات</th>
                                <th>الوحدات المصروفة</th>
                                <th>متوسط الطلب</th>
                                <th>القيمة الإجمالية</th>
                                <th>النسبة من الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody id="usageTableBody">
                            <!-- Data will be loaded via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}

.progress {
    background-color: #e9ecef;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sample data - in real implementation, this would come from the backend
const usageData = [
    {
        drug: 'بانادول 500 مجم',
        category: 'مسكنات',
        orders: 450,
        units: 15250,
        avgPerOrder: 33.9,
        value: 456750,
        percentage: 18.5
    },
    {
        drug: 'أموكسيسيلين 500 مجم',
        category: 'مضادات حيوية',
        orders: 320,
        units: 12800,
        avgPerOrder: 40.0,
        value: 384000,
        percentage: 15.5
    },
    {
        drug: 'جلوكوفاج 850 مجم',
        category: 'أدوية السكري',
        orders: 280,
        units: 9650,
        avgPerOrder: 34.5,
        value: 289500,
        percentage: 11.7
    }
];

document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadUsageTable();
});

function initializeCharts() {
    // Usage Trend Chart
    const trendCtx = document.getElementById('usageTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'عدد الطلبات',
                data: [2100, 2250, 2180, 2350, 2420, 2450],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }, {
                label: 'الوحدات المصروفة (بالآلاف)',
                data: [165, 172, 168, 178, 182, 185],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'عدد الطلبات'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'الوحدات (بالآلاف)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });

    // Category Usage Chart
    const categoryCtx = document.getElementById('categoryUsageChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['مسكنات', 'مضادات حيوية', 'أدوية السكري', 'أدوية الضغط', 'أخرى'],
            datasets: [{
                data: [28, 22, 18, 15, 17],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Weekly Pattern Chart
    const weeklyCtx = document.getElementById('weeklyPatternChart').getContext('2d');
    new Chart(weeklyCtx, {
        type: 'bar',
        data: {
            labels: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
            datasets: [{
                label: 'عدد الطلبات',
                data: [420, 580, 620, 650, 590, 280, 310],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Hourly Pattern Chart
    const hourlyCtx = document.getElementById('hourlyPatternChart').getContext('2d');
    new Chart(hourlyCtx, {
        type: 'line',
        data: {
            labels: ['6', '8', '10', '12', '14', '16', '18', '20'],
            datasets: [{
                label: 'عدد الطلبات',
                data: [45, 120, 180, 220, 250, 200, 150, 80],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                },
                x: {
                    title: {
                        display: true,
                        text: 'الساعة'
                    }
                }
            }
        }
    });
}

function loadUsageTable() {
    const tbody = document.getElementById('usageTableBody');
    tbody.innerHTML = '';

    usageData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <strong>${item.drug}</strong>
            </td>
            <td>
                <span class="badge bg-primary">${item.category}</span>
            </td>
            <td class="text-primary fw-bold">${item.orders}</td>
            <td class="text-success fw-bold">${item.units.toLocaleString()}</td>
            <td>${item.avgPerOrder.toFixed(1)}</td>
            <td class="text-info">${item.value.toLocaleString()} ريال</td>
            <td>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar" style="width: ${item.percentage}%">${item.percentage}%</div>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function generateReport() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
        showToast('تم تحديث التقرير بنجاح', 'success');
    }, 2000);
}

function toggleView(viewType) {
    const buttons = document.querySelectorAll('.btn-group .btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (viewType === 'summary') {
        showToast('عرض الملخص', 'info');
    } else {
        showToast('عرض التفاصيل', 'info');
    }
}

function exportToExcel() {
    showToast('جاري تصدير التقرير إلى Excel...', 'info');
    // Implementation for Excel export
}

function exportToPDF() {
    showToast('جاري تصدير التقرير إلى PDF...', 'info');
    // Implementation for PDF export
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
