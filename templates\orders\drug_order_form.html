{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل طلب الصرف{% else %}طلب صرف أدوية جديد{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:order_list' %}">طلبات صرف الأدوية</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل الطلب{% else %}طلب جديد{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-medical me-2"></i>
                    {% if object %}تعديل طلب الصرف: {{ object.order_number }}{% else %}طلب صرف أدوية جديد{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="orderForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات الطلب الأساسية</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.institution.id_for_label }}" class="form-label">
                                المؤسسة <span class="text-danger">*</span>
                            </label>
                            {{ form.institution }}
                            {% if form.institution.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.institution.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                اختر المؤسسة التي ستستلم الأدوية
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.department.id_for_label }}" class="form-label">
                                القسم
                            </label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.department.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                القسم المحدد داخل المؤسسة (اختياري)
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.priority.id_for_label }}" class="form-label">
                                أولوية الطلب <span class="text-danger">*</span>
                            </label>
                            {{ form.priority }}
                            {% if form.priority.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.priority.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                حدد مستوى أولوية الطلب
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">
                                تاريخ التسليم المتوقع
                            </label>
                            {{ form.expected_delivery_date }}
                            {% if form.expected_delivery_date.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.expected_delivery_date.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                التاريخ المطلوب لتسليم الطلب
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات التسليم</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.delivery_address.id_for_label }}" class="form-label">
                                عنوان التسليم <span class="text-danger">*</span>
                            </label>
                            {{ form.delivery_address }}
                            {% if form.delivery_address.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.delivery_address.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                العنوان الكامل لتسليم الأدوية
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات إضافية</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                ملاحظات
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أي ملاحظات أو تعليمات خاصة بالطلب (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'orders:order_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}تحديث الطلب{% else %}إنشاء الطلب{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Order Priority Guide -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    دليل أولويات الطلبات
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2">
                                <i class="fas fa-exclamation-triangle"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">طارئ</h6>
                                <small class="text-muted">للحالات الطارئة والحرجة</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning text-dark me-2">
                                <i class="fas fa-arrow-up"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">عالي</h6>
                                <small class="text-muted">أولوية عالية، يحتاج معالجة سريعة</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-secondary me-2">
                                <i class="fas fa-minus"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">عادي</h6>
                                <small class="text-muted">أولوية عادية، معالجة روتينية</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-light text-dark me-2">
                                <i class="fas fa-arrow-down"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">منخفض</h6>
                                <small class="text-muted">أولوية منخفضة، يمكن تأجيلها</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">إرشادات إنشاء الطلبات:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من اختيار المؤسسة الصحيحة</li>
                        <li>حدد القسم إذا كان الطلب لقسم محدد</li>
                        <li>اختر الأولوية المناسبة للطلب</li>
                        <li>أضف عنوان تسليم واضح ومفصل</li>
                        <li>حدد تاريخ التسليم المطلوب</li>
                        <li>اكتب ملاحظات واضحة إذا لزم الأمر</li>
                    </ul>
                </div>
                
                {% if object %}
                <div class="alert alert-warning">
                    <h6 class="alert-heading">تحذير:</h6>
                    <p class="mb-0 small">
                        يمكن تعديل الطلب فقط إذا كان في حالة "في الانتظار".
                        الطلبات الموافق عليها أو المكتملة لا يمكن تعديلها.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Order Information (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">رقم الطلب:</span>
                        <span><code>{{ object.order_number }}</code></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">الحالة:</span>
                        <span>
                            {% if object.status == 'pending' %}
                            <span class="badge bg-warning text-dark">في الانتظار</span>
                            {% elif object.status == 'approved' %}
                            <span class="badge bg-info">موافق عليه</span>
                            {% elif object.status == 'fulfilled' %}
                            <span class="badge bg-success">مكتمل</span>
                            {% elif object.status == 'rejected' %}
                            <span class="badge bg-danger">مرفوض</span>
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">تاريخ الإنشاء:</span>
                        <span>{{ object.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">طلب بواسطة:</span>
                        <span>{{ object.requested_by.get_full_name|default:object.requested_by.username }}</span>
                    </div>
                    {% if object.approved_by %}
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">وافق عليه:</span>
                        <span>{{ object.approved_by.get_full_name|default:object.approved_by.username }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Next Steps -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    الخطوات التالية
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    {% if not object %}
                    <ol class="mb-0">
                        <li>إنشاء الطلب</li>
                        <li>إضافة الأدوية المطلوبة</li>
                        <li>مراجعة الطلب</li>
                        <li>إرسال للموافقة</li>
                        <li>تنفيذ الطلب</li>
                    </ol>
                    {% else %}
                    <p class="mb-0">
                        بعد حفظ التعديلات، يمكنك إضافة أو تعديل الأدوية المطلوبة في صفحة تفاصيل الطلب.
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on the institution field
    const institutionField = document.getElementById('{{ form.institution.id_for_label }}');
    if (institutionField) {
        institutionField.focus();
    }
    
    // Set minimum date for expected delivery date to today
    const deliveryDateField = document.getElementById('{{ form.expected_delivery_date.id_for_label }}');
    if (deliveryDateField) {
        const today = new Date().toISOString().split('T')[0];
        deliveryDateField.setAttribute('min', today);
    }
    
    // Form validation
    document.getElementById('orderForm').addEventListener('submit', function(e) {
        const institution = document.getElementById('{{ form.institution.id_for_label }}').value;
        const priority = document.getElementById('{{ form.priority.id_for_label }}').value;
        const deliveryAddress = document.getElementById('{{ form.delivery_address.id_for_label }}').value.trim();
        
        if (!institution) {
            alert('يرجى اختيار المؤسسة');
            e.preventDefault();
            return;
        }
        
        if (!priority) {
            alert('يرجى تحديد أولوية الطلب');
            e.preventDefault();
            return;
        }
        
        if (!deliveryAddress) {
            alert('يرجى إدخال عنوان التسليم');
            e.preventDefault();
            return;
        }
    });
    
    // Auto-fill delivery address based on institution
    const institutionSelect = document.getElementById('{{ form.institution.id_for_label }}');
    const deliveryAddressField = document.getElementById('{{ form.delivery_address.id_for_label }}');
    
    if (institutionSelect && deliveryAddressField) {
        institutionSelect.addEventListener('change', function() {
            if (this.selectedOptions.length > 0 && !deliveryAddressField.value.trim()) {
                const selectedOption = this.selectedOptions[0];
                const institutionName = selectedOption.text;
                // You could fetch the institution address via AJAX here
                // For now, just suggest the user to enter the address
                deliveryAddressField.placeholder = `عنوان ${institutionName}`;
            }
        });
    }
});
</script>
{% endblock %}
