from django.urls import path
from . import views

app_name = 'procurement'

urlpatterns = [
    # Supplier URLs
    path('suppliers/', views.SupplierListView.as_view(), name='supplier_list'),
    path('suppliers/create/', views.SupplierCreateView.as_view(), name='supplier_create'),
    path('suppliers/<int:pk>/', views.SupplierDetailView.as_view(), name='supplier_detail'),
    path('suppliers/<int:pk>/edit/', views.SupplierUpdateView.as_view(), name='supplier_edit'),
    path('suppliers/<int:pk>/delete/', views.SupplierDeleteView.as_view(), name='supplier_delete'),
    path('suppliers/<int:pk>/evaluate/', views.evaluate_supplier, name='supplier_evaluate'),

    # Purchase Order URLs
    path('', views.PurchaseOrderListView.as_view(), name='purchase_order_list'),
    path('create/', views.PurchaseOrderCreateView.as_view(), name='purchase_order_create'),
    path('purchase-orders/create/', views.PurchaseOrderCreateView.as_view(), name='purchase_orders_create'),
    path('<int:pk>/', views.PurchaseOrderDetailView.as_view(), name='purchase_order_detail'),
    path('<int:pk>/edit/', views.PurchaseOrderUpdateView.as_view(), name='purchase_order_edit'),
    path('<int:pk>/delete/', views.PurchaseOrderDeleteView.as_view(), name='purchase_order_delete'),
    path('<int:pk>/send/', views.send_purchase_order, name='purchase_order_send'),
    path('<int:pk>/confirm/', views.confirm_purchase_order, name='purchase_order_confirm'),
    path('<int:pk>/receive/', views.receive_purchase_order, name='purchase_order_receive'),
    path('<int:pk>/cancel/', views.cancel_purchase_order, name='purchase_order_cancel'),
    path('<int:pk>/print/', views.print_purchase_order, name='purchase_order_print'),

    # Purchase Order Item Management
    path('<int:order_id>/items/add/', views.add_purchase_order_item, name='add_purchase_order_item'),
    path('items/<int:item_id>/edit/', views.edit_purchase_order_item, name='edit_purchase_order_item'),
    path('items/<int:item_id>/delete/', views.delete_purchase_order_item, name='delete_purchase_order_item'),
    path('items/<int:item_id>/receive/', views.receive_purchase_order_item, name='receive_purchase_order_item'),

    # Reports
    path('reports/supplier-performance/', views.SupplierPerformanceReportView.as_view(), name='supplier_performance_report'),
    path('reports/purchase-summary/', views.PurchaseSummaryReportView.as_view(), name='purchase_summary_report'),

    # API URLs
    path('api/supplier-drugs/', views.supplier_drugs_api, name='supplier_drugs_api'),
    path('api/purchase-order-status/', views.purchase_order_status_api, name='purchase_order_status_api'),
]
