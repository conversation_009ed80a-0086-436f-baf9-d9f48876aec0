from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from .models import DrugReturn, DrugReturnItem, DamagedDrug

class DrugReturnListView(LoginRequiredMixin, ListView):
    model = DrugReturn
    template_name = 'returns/drug_return_list.html'
    context_object_name = 'returns'
    paginate_by = 20

class DrugReturnDetailView(LoginRequiredMixin, DetailView):
    model = DrugReturn
    template_name = 'returns/drug_return_detail.html'
    context_object_name = 'drug_return'

class DrugReturnCreateView(LoginRequiredMixin, CreateView):
    model = DrugReturn
    template_name = 'returns/drug_return_form.html'
    fields = ['return_type', 'department', 'warehouse', 'reason', 'notes']
    success_url = reverse_lazy('returns:drug_return_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        return super().form_valid(form)

class DrugReturnUpdateView(LoginRequiredMixin, UpdateView):
    model = DrugReturn
    template_name = 'returns/drug_return_form.html'
    fields = ['return_type', 'department', 'warehouse', 'reason', 'notes']
    success_url = reverse_lazy('returns:drug_return_list')

class DrugReturnDeleteView(LoginRequiredMixin, DeleteView):
    model = DrugReturn
    template_name = 'returns/drug_return_confirm_delete.html'
    success_url = reverse_lazy('returns:drug_return_list')

class DamagedDrugListView(LoginRequiredMixin, ListView):
    model = DamagedDrug
    template_name = 'returns/damaged_drug_list.html'
    context_object_name = 'damaged_drugs'
    paginate_by = 20

class DamagedDrugDetailView(LoginRequiredMixin, DetailView):
    model = DamagedDrug
    template_name = 'returns/damaged_drug_detail.html'
    context_object_name = 'damaged_drug'

class DamagedDrugCreateView(LoginRequiredMixin, CreateView):
    model = DamagedDrug
    template_name = 'returns/damaged_drug_form.html'
    fields = ['inventory_item', 'damage_type', 'quantity_damaged', 'damage_description', 'discovery_date', 'estimated_loss', 'disposal_method']
    success_url = reverse_lazy('returns:damaged_drug_list')

    def form_valid(self, form):
        form.instance.reported_by = self.request.user
        return super().form_valid(form)

class DamagedDrugUpdateView(LoginRequiredMixin, UpdateView):
    model = DamagedDrug
    template_name = 'returns/damaged_drug_form.html'
    fields = ['inventory_item', 'damage_type', 'quantity_damaged', 'damage_description', 'discovery_date', 'estimated_loss', 'disposal_method']
    success_url = reverse_lazy('returns:damaged_drug_list')

class DamagedDrugDeleteView(LoginRequiredMixin, DeleteView):
    model = DamagedDrug
    template_name = 'returns/damaged_drug_confirm_delete.html'
    success_url = reverse_lazy('returns:damaged_drug_list')

class ReturnsSummaryReportView(LoginRequiredMixin, ListView):
    model = DrugReturn
    template_name = 'returns/returns_summary_report.html'
    context_object_name = 'returns'

class DamagedDrugsReportView(LoginRequiredMixin, ListView):
    model = DamagedDrug
    template_name = 'returns/damaged_drugs_report.html'
    context_object_name = 'damaged_drugs'

# Action Views
@login_required
def approve_drug_return(request, pk):
    drug_return = get_object_or_404(DrugReturn, pk=pk)
    drug_return.status = 'approved'
    drug_return.approved_by = request.user
    drug_return.save()
    messages.success(request, f'تم الموافقة على المرتجع {drug_return.return_number}')
    return redirect('returns:drug_return_detail', pk=pk)

@login_required
def reject_drug_return(request, pk):
    drug_return = get_object_or_404(DrugReturn, pk=pk)
    drug_return.status = 'rejected'
    drug_return.approved_by = request.user
    drug_return.save()
    messages.warning(request, f'تم رفض المرتجع {drug_return.return_number}')
    return redirect('returns:drug_return_detail', pk=pk)

@login_required
def process_drug_return(request, pk):
    drug_return = get_object_or_404(DrugReturn, pk=pk)
    drug_return.status = 'processed'
    drug_return.save()
    messages.success(request, f'تم معالجة المرتجع {drug_return.return_number}')
    return redirect('returns:drug_return_detail', pk=pk)

@login_required
def dispose_damaged_drug(request, pk):
    damaged_drug = get_object_or_404(DamagedDrug, pk=pk)
    from django.utils import timezone
    damaged_drug.disposal_date = timezone.now()
    damaged_drug.save()
    messages.success(request, f'تم التخلص من الدواء التالف {damaged_drug.damage_number}')
    return redirect('returns:damaged_drug_detail', pk=pk)

# Item Management Views
@login_required
def add_return_item(request, return_id):
    # Implementation for adding return items
    return JsonResponse({'status': 'success'})

@login_required
def edit_return_item(request, item_id):
    # Implementation for editing return items
    return JsonResponse({'status': 'success'})

@login_required
def delete_return_item(request, item_id):
    # Implementation for deleting return items
    return JsonResponse({'status': 'success'})

# API Views
def return_reasons_api(request):
    """API للحصول على أسباب المرتجعات"""
    reasons = DrugReturn.RETURN_TYPE
    return JsonResponse({'reasons': [{'value': r[0], 'label': r[1]} for r in reasons]})
