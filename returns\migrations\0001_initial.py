# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('drugs', '0001_initial'),
        ('institutions', '0001_initial'),
        ('inventory', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DamagedDrug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('damage_number', models.CharField(max_length=50, unique=True, verbose_name='رقم التلف')),
                ('damage_type', models.CharField(choices=[('expired', 'منتهي الصلاحية'), ('physical_damage', 'تلف مادي'), ('contamination', 'تلوث'), ('temperature_damage', 'تلف حراري'), ('moisture_damage', 'تلف رطوبة'), ('other', 'أخرى')], max_length=20, verbose_name='نوع التلف')),
                ('quantity_damaged', models.PositiveIntegerField(verbose_name='الكمية التالفة')),
                ('damage_description', models.TextField(verbose_name='وصف التلف')),
                ('discovery_date', models.DateTimeField(verbose_name='تاريخ اكتشاف التلف')),
                ('estimated_loss', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخسارة المقدرة')),
                ('disposal_method', models.CharField(blank=True, max_length=200, verbose_name='طريقة التخلص')),
                ('disposal_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ التخلص')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('inventory_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.inventoryitem', verbose_name='عنصر المخزون')),
                ('reported_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أبلغ بواسطة')),
            ],
            options={
                'verbose_name': 'الدواء التالف',
                'verbose_name_plural': 'الأدوية التالفة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DrugReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المرتجع')),
                ('return_type', models.CharField(choices=[('expired', 'منتهي الصلاحية'), ('damaged', 'تالف'), ('recalled', 'مسحوب'), ('excess', 'فائض'), ('wrong_item', 'صنف خاطئ'), ('quality_issue', 'مشكلة في الجودة')], max_length=20, verbose_name='نوع المرتجع')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('processed', 'تم المعالجة'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='حالة المرتجع')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_returns', to=settings.AUTH_USER_MODEL, verbose_name='وافق عليه')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_returns', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='institutions.department', verbose_name='القسم')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.warehouse', verbose_name='المستودع')),
            ],
            options={
                'verbose_name': 'مرتجع الأدوية',
                'verbose_name_plural': 'مرتجعات الأدوية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DrugReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('condition_notes', models.TextField(blank=True, verbose_name='ملاحظات الحالة')),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.drugbatch', verbose_name='الدفعة')),
                ('drug', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='drugs.drug', verbose_name='الدواء')),
                ('drug_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='returns.drugreturn', verbose_name='المرتجع')),
            ],
            options={
                'verbose_name': 'عنصر مرتجع الأدوية',
                'verbose_name_plural': 'عناصر مرتجع الأدوية',
                'unique_together': {('drug_return', 'drug', 'batch')},
            },
        ),
    ]
