{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل التصنيف{% else %}إضافة تصنيف جديد{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:category_list' %}">تصنيفات الأدوية</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل التصنيف{% else %}إضافة تصنيف جديد{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>
                    {% if object %}تعديل التصنيف: {{ object.name }}{% else %}إضافة تصنيف جديد{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-12 mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                اسم التصنيف <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.name.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أدخل اسماً واضحاً ومميزاً للتصنيف (مثل: مضادات حيوية، مسكنات الألم)
                            </div>
                        </div>
                        
                        <div class="col-12 mb-4">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                الوصف
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.description.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                وصف مختصر للتصنيف وأنواع الأدوية التي يشملها (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'drugs:category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}تحديث التصنيف{% else %}إضافة التصنيف{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Help Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-0">
                    <h6 class="alert-heading">إرشادات إنشاء التصنيفات:</h6>
                    <ul class="mb-0 small">
                        <li>استخدم أسماء واضحة ومفهومة للتصنيفات</li>
                        <li>تجنب التكرار في أسماء التصنيفات</li>
                        <li>اجعل التصنيفات عامة بما يكفي لتشمل عدة أدوية</li>
                        <li>أضف وصفاً مفيداً يوضح نوع الأدوية المشمولة</li>
                        <li>فكر في التصنيفات الطبية المعتمدة عالمياً</li>
                    </ul>
                </div>
                
                {% if object %}
                <div class="alert alert-warning mt-3 mb-0">
                    <h6 class="alert-heading">تحذير:</h6>
                    <p class="mb-0 small">
                        تعديل اسم التصنيف قد يؤثر على تنظيم الأدوية الحالية. 
                        تأكد من أن الاسم الجديد مناسب لجميع الأدوية المرتبطة بهذا التصنيف.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Related Drugs (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-pills me-2"></i>
                    الأدوية المرتبطة بهذا التصنيف ({{ object.drug_set.count }})
                </h6>
            </div>
            <div class="card-body">
                {% if object.drug_set.exists %}
                <div class="row">
                    {% for drug in object.drug_set.all|slice:":6" %}
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-pill text-primary me-2"></i>
                            <span class="small">{{ drug.commercial_name }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if object.drug_set.count > 6 %}
                <div class="text-center mt-3">
                    <a href="{% url 'drugs:drug_list' %}?category={{ object.id }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الأدوية ({{ object.drug_set.count }})
                    </a>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-info-circle text-muted me-2"></i>
                    <span class="text-muted">لا توجد أدوية مرتبطة بهذا التصنيف حالياً</span>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on the name field
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    if (nameField) {
        nameField.focus();
    }
    
    // Character counter for description
    const descriptionField = document.getElementById('{{ form.description.id_for_label }}');
    if (descriptionField) {
        const maxLength = 500;
        const counter = document.createElement('div');
        counter.className = 'form-text text-end';
        counter.innerHTML = `<span id="char-count">0</span> / ${maxLength} حرف`;
        descriptionField.parentNode.appendChild(counter);
        
        const charCountSpan = document.getElementById('char-count');
        
        function updateCounter() {
            const currentLength = descriptionField.value.length;
            charCountSpan.textContent = currentLength;
            
            if (currentLength > maxLength * 0.9) {
                charCountSpan.className = 'text-warning';
            } else if (currentLength > maxLength * 0.8) {
                charCountSpan.className = 'text-info';
            } else {
                charCountSpan.className = '';
            }
        }
        
        descriptionField.addEventListener('input', updateCounter);
        updateCounter(); // Initial count
    }
});
</script>
{% endblock %}
