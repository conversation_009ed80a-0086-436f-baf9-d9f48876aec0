{% extends 'base/base.html' %}
{% load static %}

{% block title %}لوحة التحكم - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item active">لوحة التحكم</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة التحكم
        </h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-start border-primary border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الأدوية
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_drugs|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-pills fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-start border-success border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            المخزون المتاح
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ available_stock|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-warehouse fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-start border-warning border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            طلبات في الانتظار
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ pending_orders|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-start border-danger border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            مخزون منخفض
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ low_stock_items|default:0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'drugs:drug_create' %}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دواء جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'orders:order_create' %}" class="btn btn-success w-100">
                            <i class="fas fa-shopping-cart me-2"></i>
                            طلب صرف جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-info w-100">
                            <i class="fas fa-truck me-2"></i>
                            أمر شراء جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'inventory:movement_create' %}" class="btn btn-warning w-100">
                            <i class="fas fa-exchange-alt me-2"></i>
                            حركة مخزون
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Alerts -->
<div class="row">
    <!-- Recent Orders -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    آخر الطلبات
                </h5>
                <a href="{% url 'orders:order_list' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_orders %}
                <div class="list-group list-group-flush">
                    {% for order in recent_orders %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ order.order_number }}</h6>
                            <p class="mb-1 text-muted">{{ order.institution.name }}</p>
                            <small class="text-muted">{{ order.created_at|date:"d/m/Y H:i" }}</small>
                        </div>
                        <span class="badge bg-{{ order.status|default:'secondary' }} rounded-pill">
                            {{ order.get_status_display }}
                        </span>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد طلبات حديثة</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- System Alerts -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>
                    تنبيهات النظام
                </h5>
                <a href="{% url 'reports:reports_dashboard' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if system_alerts %}
                <div class="list-group list-group-flush">
                    {% for alert in system_alerts %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">
                                <i class="fas fa-exclamation-circle text-{{ alert.severity }} me-2"></i>
                                {{ alert.title }}
                            </h6>
                            <small class="text-muted">{{ alert.created_at|date:"d/m/Y" }}</small>
                        </div>
                        <p class="mb-1">{{ alert.description|truncatewords:15 }}</p>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted text-center">لا توجد تنبيهات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    إحصائيات الطلبات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="ordersChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع حالة الطلبات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Orders Chart
const ordersCtx = document.getElementById('ordersChart').getContext('2d');
const ordersChart = new Chart(ordersCtx, {
    type: 'line',
    data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'الطلبات',
            data: [12, 19, 3, 5, 2, 3],
            borderColor: 'rgb(75, 192, 192)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Status Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['في الانتظار', 'موافق عليه', 'مكتمل', 'مرفوض'],
        datasets: [{
            data: [30, 25, 35, 10],
            backgroundColor: [
                '#ffc107',
                '#198754',
                '#0d6efd',
                '#dc3545'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});
</script>
{% endblock %}
