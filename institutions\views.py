from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from .models import Institution, Department

class InstitutionListView(LoginRequiredMixin, ListView):
    model = Institution
    template_name = 'institutions/institution_list.html'
    context_object_name = 'institutions'
    paginate_by = 20

class InstitutionDetailView(LoginRequiredMixin, DetailView):
    model = Institution
    template_name = 'institutions/institution_detail.html'
    context_object_name = 'institution'

class InstitutionCreateView(LoginRequiredMixin, CreateView):
    model = Institution
    template_name = 'institutions/institution_form.html'
    fields = ['name', 'institution_type', 'license_number', 'address', 'city', 'phone', 'email', 'contact_person', 'contact_person_phone', 'credit_limit']
    success_url = reverse_lazy('institutions:institution_list')

class InstitutionUpdateView(LoginRequiredMixin, UpdateView):
    model = Institution
    template_name = 'institutions/institution_form.html'
    fields = ['name', 'institution_type', 'license_number', 'address', 'city', 'phone', 'email', 'contact_person', 'contact_person_phone', 'credit_limit']
    success_url = reverse_lazy('institutions:institution_list')

class InstitutionDeleteView(LoginRequiredMixin, DeleteView):
    model = Institution
    template_name = 'institutions/institution_confirm_delete.html'
    success_url = reverse_lazy('institutions:institution_list')

class DepartmentListView(LoginRequiredMixin, ListView):
    model = Department
    template_name = 'institutions/department_list.html'
    context_object_name = 'departments'
    paginate_by = 20

class DepartmentDetailView(LoginRequiredMixin, DetailView):
    model = Department
    template_name = 'institutions/department_detail.html'
    context_object_name = 'department'

class DepartmentCreateView(LoginRequiredMixin, CreateView):
    model = Department
    template_name = 'institutions/department_form.html'
    fields = ['institution', 'name', 'code', 'head_of_department', 'phone', 'location', 'can_request_drugs']
    success_url = reverse_lazy('institutions:department_list')

class DepartmentUpdateView(LoginRequiredMixin, UpdateView):
    model = Department
    template_name = 'institutions/department_form.html'
    fields = ['institution', 'name', 'code', 'head_of_department', 'phone', 'location', 'can_request_drugs']
    success_url = reverse_lazy('institutions:department_list')

class DepartmentDeleteView(LoginRequiredMixin, DeleteView):
    model = Department
    template_name = 'institutions/department_confirm_delete.html'
    success_url = reverse_lazy('institutions:department_list')

# API Views
def institution_departments_api(request, institution_id):
    """API للحصول على أقسام مؤسسة معينة"""
    departments = Department.objects.filter(
        institution_id=institution_id,
        is_active=True
    ).values('id', 'name', 'code')

    return JsonResponse({'departments': list(departments)})
