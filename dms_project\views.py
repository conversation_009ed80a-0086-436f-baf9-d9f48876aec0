from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, F
from drugs.models import Drug
from inventory.models import InventoryItem
from orders.models import DrugOrder, InternalRequest
from reports.models import SystemAlert

@login_required
def dashboard(request):
    """لوحة التحكم الرئيسية"""

    # إحصائيات أساسية
    total_drugs = Drug.objects.filter(is_active=True).count()

    # المخزون المتاح
    available_stock = InventoryItem.objects.aggregate(
        total=Sum('quantity')
    )['total'] or 0

    # الطلبات في الانتظار
    pending_orders = DrugOrder.objects.filter(status='pending').count()

    # المخزون المنخفض
    low_stock_items = InventoryItem.objects.filter(
        quantity__lte=F('minimum_stock')
    ).count()

    # آخر الطلبات
    recent_orders = DrugOrder.objects.select_related('institution').order_by('-created_at')[:5]

    # تنبيهات النظام
    system_alerts = SystemAlert.objects.filter(is_active=True).order_by('-created_at')[:5]

    context = {
        'total_drugs': total_drugs,
        'available_stock': available_stock,
        'pending_orders': pending_orders,
        'low_stock_items': low_stock_items,
        'recent_orders': recent_orders,
        'system_alerts': system_alerts,
    }

    return render(request, 'dashboard.html', context)
