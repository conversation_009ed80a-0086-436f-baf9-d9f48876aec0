from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from .models import Supplier, PurchaseOrder, PurchaseOrderItem

class SupplierListView(LoginRequiredMixin, ListView):
    model = Supplier
    template_name = 'procurement/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20

class SupplierDetailView(LoginRequiredMixin, DetailView):
    model = Supplier
    template_name = 'procurement/supplier_detail.html'
    context_object_name = 'supplier'

class SupplierCreateView(LoginRequiredMixin, CreateView):
    model = Supplier
    template_name = 'procurement/supplier_form.html'
    fields = ['name', 'company_registration', 'tax_number', 'address', 'city', 'country', 'phone', 'email', 'website', 'contact_person', 'contact_phone', 'contact_email', 'credit_limit', 'payment_terms']
    success_url = reverse_lazy('procurement:supplier_list')

class SupplierUpdateView(LoginRequiredMixin, UpdateView):
    model = Supplier
    template_name = 'procurement/supplier_form.html'
    fields = ['name', 'company_registration', 'tax_number', 'address', 'city', 'country', 'phone', 'email', 'website', 'contact_person', 'contact_phone', 'contact_email', 'credit_limit', 'payment_terms']
    success_url = reverse_lazy('procurement:supplier_list')

class SupplierDeleteView(LoginRequiredMixin, DeleteView):
    model = Supplier
    template_name = 'procurement/supplier_confirm_delete.html'
    success_url = reverse_lazy('procurement:supplier_list')

class PurchaseOrderListView(LoginRequiredMixin, ListView):
    model = PurchaseOrder
    template_name = 'procurement/purchase_order_list.html'
    context_object_name = 'purchase_orders'
    paginate_by = 20

class PurchaseOrderDetailView(LoginRequiredMixin, DetailView):
    model = PurchaseOrder
    template_name = 'procurement/purchase_order_detail.html'
    context_object_name = 'purchase_order'

class PurchaseOrderCreateView(LoginRequiredMixin, CreateView):
    model = PurchaseOrder
    template_name = 'procurement/purchase_order_form.html'
    fields = ['supplier', 'notes', 'delivery_address', 'expected_delivery_date']
    success_url = reverse_lazy('procurement:purchase_order_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        return super().form_valid(form)

class PurchaseOrderUpdateView(LoginRequiredMixin, UpdateView):
    model = PurchaseOrder
    template_name = 'procurement/purchase_order_form.html'
    fields = ['supplier', 'notes', 'delivery_address', 'expected_delivery_date']
    success_url = reverse_lazy('procurement:purchase_order_list')

class PurchaseOrderDeleteView(LoginRequiredMixin, DeleteView):
    model = PurchaseOrder
    template_name = 'procurement/purchase_order_confirm_delete.html'
    success_url = reverse_lazy('procurement:purchase_order_list')

class SupplierPerformanceReportView(LoginRequiredMixin, ListView):
    model = Supplier
    template_name = 'procurement/supplier_performance_report.html'
    context_object_name = 'suppliers'

class PurchaseSummaryReportView(LoginRequiredMixin, ListView):
    model = PurchaseOrder
    template_name = 'procurement/purchase_summary_report.html'
    context_object_name = 'purchase_orders'

# Action Views
@login_required
def send_purchase_order(request, pk):
    purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
    purchase_order.status = 'sent'
    purchase_order.save()
    messages.success(request, f'تم إرسال أمر الشراء {purchase_order.order_number}')
    return redirect('procurement:purchase_order_detail', pk=pk)

@login_required
def confirm_purchase_order(request, pk):
    purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
    purchase_order.status = 'confirmed'
    purchase_order.save()
    messages.success(request, f'تم تأكيد أمر الشراء {purchase_order.order_number}')
    return redirect('procurement:purchase_order_detail', pk=pk)

@login_required
def receive_purchase_order(request, pk):
    purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
    purchase_order.status = 'received'
    purchase_order.save()
    messages.success(request, f'تم استلام أمر الشراء {purchase_order.order_number}')
    return redirect('procurement:purchase_order_detail', pk=pk)

@login_required
def cancel_purchase_order(request, pk):
    purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
    purchase_order.status = 'cancelled'
    purchase_order.save()
    messages.warning(request, f'تم إلغاء أمر الشراء {purchase_order.order_number}')
    return redirect('procurement:purchase_order_detail', pk=pk)

@login_required
def print_purchase_order(request, pk):
    purchase_order = get_object_or_404(PurchaseOrder, pk=pk)
    # Here you would implement PDF generation
    return HttpResponse(f"طباعة أمر الشراء {purchase_order.order_number}")

@login_required
def evaluate_supplier(request, pk):
    supplier = get_object_or_404(Supplier, pk=pk)
    # Implementation for supplier evaluation
    messages.success(request, f'تم تقييم المورد {supplier.name}')
    return redirect('procurement:supplier_detail', pk=pk)

# Item Management Views
@login_required
def add_purchase_order_item(request, order_id):
    # Implementation for adding purchase order items
    return JsonResponse({'status': 'success'})

@login_required
def edit_purchase_order_item(request, item_id):
    # Implementation for editing purchase order items
    return JsonResponse({'status': 'success'})

@login_required
def delete_purchase_order_item(request, item_id):
    # Implementation for deleting purchase order items
    return JsonResponse({'status': 'success'})

@login_required
def receive_purchase_order_item(request, item_id):
    # Implementation for receiving purchase order items
    return JsonResponse({'status': 'success'})

# API Views
def supplier_drugs_api(request):
    """API للحصول على أدوية الموردين"""
    suppliers = Supplier.objects.filter(is_active=True).values('id', 'name')
    return JsonResponse({'suppliers': list(suppliers)})

def purchase_order_status_api(request):
    """API للحصول على حالة أوامر الشراء"""
    orders = PurchaseOrder.objects.values('id', 'order_number', 'status', 'created_at')
    return JsonResponse({'orders': list(orders)})
