{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل أمر الشراء{% else %}أمر شراء جديد{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'procurement:purchase_order_list' %}">أوامر الشراء</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل الأمر{% else %}أمر جديد{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>
                    {% if object %}تعديل أمر الشراء: {{ object.order_number }}{% else %}أمر شراء جديد{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="purchaseOrderForm">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات الأمر الأساسية</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.supplier.id_for_label }}" class="form-label">
                                المورد <span class="text-danger">*</span>
                            </label>
                            {{ form.supplier }}
                            {% if form.supplier.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.supplier.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                اختر المورد الذي ستشتري منه الأدوية
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">
                                تاريخ التسليم المتوقع <span class="text-danger">*</span>
                            </label>
                            {{ form.expected_delivery_date }}
                            {% if form.expected_delivery_date.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.expected_delivery_date.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                التاريخ المطلوب لتسليم الطلب
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivery Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات التسليم</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.delivery_address.id_for_label }}" class="form-label">
                                عنوان التسليم <span class="text-danger">*</span>
                            </label>
                            {{ form.delivery_address }}
                            {% if form.delivery_address.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.delivery_address.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                العنوان الكامل لتسليم الأدوية
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات إضافية</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                ملاحظات
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أي ملاحظات أو تعليمات خاصة بالأمر (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'procurement:purchase_order_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}تحديث الأمر{% else %}إنشاء الأمر{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Purchase Order Process -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    مراحل أمر الشراء
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning text-dark me-2">
                                <i class="fas fa-edit"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مسودة</h6>
                                <small class="text-muted">إنشاء وتعديل الأمر</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">
                                <i class="fas fa-paper-plane"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مرسل</h6>
                                <small class="text-muted">إرسال الأمر للمورد</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-check"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مؤكد</h6>
                                <small class="text-muted">تأكيد المورد للأمر</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-truck"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">قيد التسليم</h6>
                                <small class="text-muted">الأدوية في الطريق</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-check-double"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">مستلم</h6>
                                <small class="text-muted">اكتمال الاستلام</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">إرشادات إنشاء أوامر الشراء:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من اختيار المورد المناسب</li>
                        <li>حدد تاريخ تسليم واقعي</li>
                        <li>أضف عنوان تسليم واضح ومفصل</li>
                        <li>اكتب ملاحظات واضحة إذا لزم الأمر</li>
                        <li>راجع معلومات المورد قبل الإرسال</li>
                        <li>تأكد من توفر الميزانية المطلوبة</li>
                    </ul>
                </div>
                
                {% if object %}
                <div class="alert alert-warning">
                    <h6 class="alert-heading">تحذير:</h6>
                    <p class="mb-0 small">
                        يمكن تعديل الأمر فقط إذا كان في حالة "مسودة".
                        الأوامر المرسلة أو المؤكدة لا يمكن تعديلها.
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Supplier Information (if editing) -->
        {% if object and object.supplier %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-truck me-2"></i>
                    معلومات المورد
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>{{ object.supplier.name }}</strong>
                    </div>
                    <div class="mb-2">
                        <span class="text-muted">الهاتف:</span>
                        <span>{{ object.supplier.phone }}</span>
                    </div>
                    <div class="mb-2">
                        <span class="text-muted">البريد:</span>
                        <span>{{ object.supplier.email }}</span>
                    </div>
                    <div class="mb-2">
                        <span class="text-muted">المدينة:</span>
                        <span>{{ object.supplier.city }}, {{ object.supplier.country }}</span>
                    </div>
                    <div class="mb-2">
                        <span class="text-muted">التقييم:</span>
                        <span class="text-warning">
                            {% for i in "12345" %}
                                {% if forloop.counter <= object.supplier.rating %}
                                <i class="fas fa-star"></i>
                                {% else %}
                                <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Order Information (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات الأمر
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">رقم الأمر:</span>
                        <span><code>{{ object.order_number }}</code></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">الحالة:</span>
                        <span>
                            {% if object.status == 'draft' %}
                            <span class="badge bg-warning text-dark">مسودة</span>
                            {% elif object.status == 'sent' %}
                            <span class="badge bg-info">مرسل</span>
                            {% elif object.status == 'confirmed' %}
                            <span class="badge bg-success">مؤكد</span>
                            {% elif object.status == 'received' %}
                            <span class="badge bg-success">مستلم</span>
                            {% elif object.status == 'cancelled' %}
                            <span class="badge bg-danger">ملغي</span>
                            {% endif %}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">تاريخ الإنشاء:</span>
                        <span>{{ object.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">أنشأ بواسطة:</span>
                        <span>{{ object.created_by.get_full_name|default:object.created_by.username }}</span>
                    </div>
                    {% if object.approved_by %}
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">وافق عليه:</span>
                        <span>{{ object.approved_by.get_full_name|default:object.approved_by.username }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Next Steps -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    الخطوات التالية
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    {% if not object %}
                    <ol class="mb-0">
                        <li>إنشاء أمر الشراء</li>
                        <li>إضافة الأدوية المطلوبة</li>
                        <li>مراجعة الأمر والأسعار</li>
                        <li>إرسال الأمر للمورد</li>
                        <li>متابعة التأكيد والتسليم</li>
                    </ol>
                    {% else %}
                    <p class="mb-0">
                        بعد حفظ التعديلات، يمكنك إضافة أو تعديل الأدوية المطلوبة في صفحة تفاصيل الأمر.
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus on the supplier field
    const supplierField = document.getElementById('{{ form.supplier.id_for_label }}');
    if (supplierField) {
        supplierField.focus();
    }
    
    // Set minimum date for expected delivery date to today
    const deliveryDateField = document.getElementById('{{ form.expected_delivery_date.id_for_label }}');
    if (deliveryDateField) {
        const today = new Date().toISOString().split('T')[0];
        deliveryDateField.setAttribute('min', today);
    }
    
    // Form validation
    document.getElementById('purchaseOrderForm').addEventListener('submit', function(e) {
        const supplier = document.getElementById('{{ form.supplier.id_for_label }}').value;
        const deliveryDate = document.getElementById('{{ form.expected_delivery_date.id_for_label }}').value;
        const deliveryAddress = document.getElementById('{{ form.delivery_address.id_for_label }}').value.trim();
        
        if (!supplier) {
            alert('يرجى اختيار المورد');
            e.preventDefault();
            return;
        }
        
        if (!deliveryDate) {
            alert('يرجى تحديد تاريخ التسليم المتوقع');
            e.preventDefault();
            return;
        }
        
        if (!deliveryAddress) {
            alert('يرجى إدخال عنوان التسليم');
            e.preventDefault();
            return;
        }
    });
    
    // Auto-fill delivery address based on supplier
    const supplierSelect = document.getElementById('{{ form.supplier.id_for_label }}');
    const deliveryAddressField = document.getElementById('{{ form.delivery_address.id_for_label }}');
    
    if (supplierSelect && deliveryAddressField) {
        supplierSelect.addEventListener('change', function() {
            if (this.selectedOptions.length > 0 && !deliveryAddressField.value.trim()) {
                // You could fetch the supplier address via AJAX here
                // For now, just suggest the user to enter the address
                deliveryAddressField.placeholder = 'عنوان التسليم للمورد المختار';
            }
        });
    }
});
</script>
{% endblock %}
