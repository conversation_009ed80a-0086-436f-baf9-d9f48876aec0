from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal
from drugs.models import Drug
from institutions.models import Institution, Department
from inventory.models import Warehouse, InventoryItem

class DrugOrder(models.Model):
    """طلبات صرف الأدوية للمؤسسات"""
    ORDER_STATUS = [
        ('pending', 'في الانتظار'),
        ('approved', 'موافق عليه'),
        ('partially_fulfilled', 'مكتمل جزئياً'),
        ('fulfilled', 'مكتمل'),
        ('rejected', 'مرفوض'),
        ('cancelled', 'ملغي'),
    ]

    ORDER_PRIORITY = [
        ('low', 'منخفض'),
        ('normal', 'عادي'),
        ('high', 'عالي'),
        ('urgent', 'طارئ'),
    ]

    # معلومات أساسية
    order_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    institution = models.ForeignKey(Institution, on_delete=models.CASCADE, related_name='drug_orders', verbose_name="المؤسسة")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True, verbose_name="القسم")

    # حالة الطلب
    status = models.CharField(max_length=20, choices=ORDER_STATUS, default='pending', verbose_name="حالة الطلب")
    priority = models.CharField(max_length=10, choices=ORDER_PRIORITY, default='normal', verbose_name="الأولوية")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")
    delivery_address = models.TextField(verbose_name="عنوان التسليم")
    expected_delivery_date = models.DateField(null=True, blank=True, verbose_name="تاريخ التسليم المتوقع")

    # معلومات المستخدم
    requested_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='requested_orders', verbose_name="طلب بواسطة")
    approved_by = models.ForeignKey(User, on_delete=models.PROTECT, null=True, blank=True, related_name='approved_orders', verbose_name="وافق عليه")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")

    class Meta:
        verbose_name = "طلب صرف الأدوية"
        verbose_name_plural = "طلبات صرف الأدوية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.order_number} - {self.institution.name}"

    @property
    def total_amount(self):
        """إجمالي قيمة الطلب"""
        return sum(item.total_price for item in self.items.all())

    @property
    def total_items(self):
        """إجمالي عدد الأصناف"""
        return self.items.count()

    def generate_order_number(self):
        """توليد رقم الطلب"""
        from django.utils import timezone
        today = timezone.now().date()
        count = DrugOrder.objects.filter(created_at__date=today).count() + 1
        return f"ORD-{today.strftime('%Y%m%d')}-{count:04d}"

    def save(self, *args, **kwargs):
        if not self.order_number:
            self.order_number = self.generate_order_number()
        super().save(*args, **kwargs)

class DrugOrderItem(models.Model):
    """عناصر طلب صرف الأدوية"""
    order = models.ForeignKey(DrugOrder, on_delete=models.CASCADE, related_name='items', verbose_name="الطلب")
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, verbose_name="الدواء")

    # الكميات
    requested_quantity = models.PositiveIntegerField(verbose_name="الكمية المطلوبة")
    approved_quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية الموافق عليها")
    delivered_quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية المسلمة")

    # السعر
    unit_price = models.DecimalField(max_digits=10, decimal_places=2,
                                   validators=[MinValueValidator(Decimal('0.01'))],
                                   verbose_name="سعر الوحدة")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر طلب الصرف"
        verbose_name_plural = "عناصر طلب الصرف"
        unique_together = ['order', 'drug']

    def __str__(self):
        return f"{self.order.order_number} - {self.drug.commercial_name}"

    @property
    def total_price(self):
        """إجمالي السعر للعنصر"""
        return self.approved_quantity * self.unit_price

    @property
    def is_fully_delivered(self):
        """فحص اكتمال التسليم"""
        return self.delivered_quantity >= self.approved_quantity

    @property
    def remaining_quantity(self):
        """الكمية المتبقية للتسليم"""
        return self.approved_quantity - self.delivered_quantity

class InternalRequest(models.Model):
    """الطلبات الداخلية من الأقسام"""
    REQUEST_STATUS = [
        ('pending', 'في الانتظار'),
        ('approved', 'موافق عليه'),
        ('partially_fulfilled', 'مكتمل جزئياً'),
        ('fulfilled', 'مكتمل'),
        ('rejected', 'مرفوض'),
        ('cancelled', 'ملغي'),
    ]

    REQUEST_TYPE = [
        ('routine', 'روتيني'),
        ('urgent', 'طارئ'),
        ('emergency', 'طوارئ'),
    ]

    # معلومات أساسية
    request_number = models.CharField(max_length=50, unique=True, verbose_name="رقم الطلب")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='internal_requests', verbose_name="القسم")
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, verbose_name="المستودع")

    # نوع وحالة الطلب
    request_type = models.CharField(max_length=20, choices=REQUEST_TYPE, default='routine', verbose_name="نوع الطلب")
    status = models.CharField(max_length=20, choices=REQUEST_STATUS, default='pending', verbose_name="حالة الطلب")

    # معلومات إضافية
    justification = models.TextField(verbose_name="المبرر")
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    # معلومات المستخدم
    requested_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='internal_requests', verbose_name="طلب بواسطة")
    approved_by = models.ForeignKey(User, on_delete=models.PROTECT, null=True, blank=True, related_name='approved_internal_requests', verbose_name="وافق عليه")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الموافقة")
    needed_by = models.DateTimeField(verbose_name="مطلوب بتاريخ")

    class Meta:
        verbose_name = "الطلب الداخلي"
        verbose_name_plural = "الطلبات الداخلية"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.request_number} - {self.department.name}"

    def generate_request_number(self):
        """توليد رقم الطلب"""
        from django.utils import timezone
        today = timezone.now().date()
        count = InternalRequest.objects.filter(created_at__date=today).count() + 1
        return f"REQ-{today.strftime('%Y%m%d')}-{count:04d}"

    def save(self, *args, **kwargs):
        if not self.request_number:
            self.request_number = self.generate_request_number()
        super().save(*args, **kwargs)

class InternalRequestItem(models.Model):
    """عناصر الطلب الداخلي"""
    request = models.ForeignKey(InternalRequest, on_delete=models.CASCADE, related_name='items', verbose_name="الطلب")
    drug = models.ForeignKey(Drug, on_delete=models.CASCADE, verbose_name="الدواء")

    # الكميات
    requested_quantity = models.PositiveIntegerField(verbose_name="الكمية المطلوبة")
    approved_quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية الموافق عليها")
    delivered_quantity = models.PositiveIntegerField(default=0, verbose_name="الكمية المسلمة")

    # معلومات إضافية
    notes = models.TextField(blank=True, verbose_name="ملاحظات")

    class Meta:
        verbose_name = "عنصر الطلب الداخلي"
        verbose_name_plural = "عناصر الطلب الداخلي"
        unique_together = ['request', 'drug']

    def __str__(self):
        return f"{self.request.request_number} - {self.drug.commercial_name}"

    @property
    def is_fully_delivered(self):
        """فحص اكتمال التسليم"""
        return self.delivered_quantity >= self.approved_quantity

    @property
    def remaining_quantity(self):
        """الكمية المتبقية للتسليم"""
        return self.approved_quantity - self.delivered_quantity
