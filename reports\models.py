from django.db import models
from django.contrib.auth.models import User

class Notification(models.Model):
    """التنبيهات والإشعارات"""
    NOTIFICATION_TYPES = [
        ('low_stock', 'انخفاض المخزون'),
        ('expiry_warning', 'تحذير انتهاء الصلاحية'),
        ('order_approval', 'موافقة الطلب'),
        ('order_rejection', 'رفض الطلب'),
        ('new_order', 'طلب جديد'),
        ('delivery_reminder', 'تذكير التسليم'),
        ('system_alert', 'تنبيه النظام'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'منخفض'),
        ('normal', 'عادي'),
        ('high', 'عالي'),
        ('urgent', 'طارئ'),
    ]

    # معلومات أساسية
    title = models.CharField(max_length=200, verbose_name="العنوان")
    message = models.TextField(verbose_name="الرسالة")
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, verbose_name="نوع التنبيه")
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal', verbose_name="الأولوية")

    # المستلمين
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications', verbose_name="المستلم")

    # حالة التنبيه
    is_read = models.BooleanField(default=False, verbose_name="مقروء")
    is_sent = models.BooleanField(default=False, verbose_name="مرسل")

    # معلومات إضافية
    related_object_id = models.PositiveIntegerField(null=True, blank=True, verbose_name="معرف الكائن المرتبط")
    related_object_type = models.CharField(max_length=50, blank=True, verbose_name="نوع الكائن المرتبط")
    action_url = models.URLField(blank=True, verbose_name="رابط الإجراء")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    read_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ القراءة")
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الإرسال")

    class Meta:
        verbose_name = "التنبيه"
        verbose_name_plural = "التنبيهات"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.recipient.get_full_name()}"

class SystemAlert(models.Model):
    """تنبيهات النظام"""
    ALERT_TYPES = [
        ('low_stock', 'انخفاض المخزون'),
        ('expiry_soon', 'انتهاء صلاحية قريب'),
        ('expired_drugs', 'أدوية منتهية الصلاحية'),
        ('overdue_orders', 'طلبات متأخرة'),
        ('system_maintenance', 'صيانة النظام'),
    ]

    SEVERITY_LEVELS = [
        ('info', 'معلومات'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
        ('critical', 'حرج'),
    ]

    # معلومات أساسية
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES, verbose_name="نوع التنبيه")
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, verbose_name="مستوى الخطورة")
    title = models.CharField(max_length=200, verbose_name="العنوان")
    description = models.TextField(verbose_name="الوصف")

    # حالة التنبيه
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_resolved = models.BooleanField(default=False, verbose_name="تم الحل")

    # معلومات إضافية
    affected_count = models.PositiveIntegerField(default=0, verbose_name="عدد المتأثرين")
    resolution_notes = models.TextField(blank=True, verbose_name="ملاحظات الحل")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="تاريخ الحل")

    class Meta:
        verbose_name = "تنبيه النظام"
        verbose_name_plural = "تنبيهات النظام"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} ({self.get_severity_display()})"

class ReportTemplate(models.Model):
    """قوالب التقارير"""
    REPORT_TYPES = [
        ('inventory', 'تقرير المخزون'),
        ('sales', 'تقرير المبيعات'),
        ('orders', 'تقرير الطلبات'),
        ('expiry', 'تقرير انتهاء الصلاحية'),
        ('suppliers', 'تقرير الموردين'),
        ('financial', 'تقرير مالي'),
        ('usage', 'تقرير الاستخدام'),
    ]

    # معلومات أساسية
    name = models.CharField(max_length=200, verbose_name="اسم القالب")
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, verbose_name="نوع التقرير")
    description = models.TextField(blank=True, verbose_name="الوصف")

    # إعدادات التقرير
    query_parameters = models.JSONField(default=dict, verbose_name="معاملات الاستعلام")
    columns = models.JSONField(default=list, verbose_name="الأعمدة")
    filters = models.JSONField(default=dict, verbose_name="المرشحات")

    # حالة القالب
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    is_public = models.BooleanField(default=False, verbose_name="عام")

    # معلومات المستخدم
    created_by = models.ForeignKey(User, on_delete=models.PROTECT, verbose_name="أنشأ بواسطة")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "قالب التقرير"
        verbose_name_plural = "قوالب التقارير"
        ordering = ['name']

    def __str__(self):
        return self.name

class GeneratedReport(models.Model):
    """التقارير المولدة"""
    # معلومات أساسية
    template = models.ForeignKey(ReportTemplate, on_delete=models.CASCADE, related_name='generated_reports', verbose_name="القالب")
    title = models.CharField(max_length=200, verbose_name="عنوان التقرير")

    # معلومات التوليد
    parameters = models.JSONField(default=dict, verbose_name="المعاملات المستخدمة")
    file_path = models.FileField(upload_to='reports/', blank=True, null=True, verbose_name="ملف التقرير")

    # إحصائيات
    total_records = models.PositiveIntegerField(default=0, verbose_name="إجمالي السجلات")
    generation_time = models.DurationField(null=True, blank=True, verbose_name="وقت التوليد")

    # معلومات المستخدم
    generated_by = models.ForeignKey(User, on_delete=models.PROTECT, verbose_name="ولد بواسطة")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التوليد")

    class Meta:
        verbose_name = "التقرير المولد"
        verbose_name_plural = "التقارير المولدة"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.title} - {self.created_at.strftime('%Y-%m-%d')}"
