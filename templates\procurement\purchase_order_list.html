{% extends 'base/base.html' %}
{% load static %}

{% block title %}أوامر الشراء - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">أوامر الشراء</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart me-2"></i>
                أوامر الشراء
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    أمر شراء جديد
                </a>
                <a href="{% url 'procurement:supplier_list' %}" class="btn btn-outline-info">
                    <i class="fas fa-truck me-2"></i>
                    الموردين
                </a>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-file-export me-2"></i>
                        التقارير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'procurement:purchase_summary_report' %}">ملخص المشتريات</a></li>
                        <li><a class="dropdown-item" href="{% url 'procurement:supplier_performance_report' %}">أداء الموردين</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">تصدير Excel</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ purchase_orders|length }}</h4>
                <small class="text-muted">إجمالي الأوامر</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with draft_count=0 %}
                        {% for order in purchase_orders %}
                            {% if order.status == 'draft' %}
                                {% with draft_count=draft_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ draft_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مسودة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% with sent_count=0 %}
                        {% for order in purchase_orders %}
                            {% if order.status == 'sent' %}
                                {% with sent_count=sent_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ sent_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مرسلة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with confirmed_count=0 %}
                        {% for order in purchase_orders %}
                            {% if order.status == 'confirmed' %}
                                {% with confirmed_count=confirmed_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ confirmed_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مؤكدة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">
                    {% with received_count=0 %}
                        {% for order in purchase_orders %}
                            {% if order.status == 'received' %}
                                {% with received_count=received_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ received_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">مستلمة</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with cancelled_count=0 %}
                        {% for order in purchase_orders %}
                            {% if order.status == 'cancelled' %}
                                {% with cancelled_count=cancelled_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ cancelled_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">ملغية</small>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.GET.search }}" placeholder="رقم الأمر أو اسم المورد...">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>مسودة</option>
                            <option value="sent" {% if request.GET.status == 'sent' %}selected{% endif %}>مرسل</option>
                            <option value="confirmed" {% if request.GET.status == 'confirmed' %}selected{% endif %}>مؤكد</option>
                            <option value="received" {% if request.GET.status == 'received' %}selected{% endif %}>مستلم</option>
                            <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="supplier" class="form-label">المورد</label>
                        <select class="form-select" id="supplier" name="supplier">
                            <option value="">جميع الموردين</option>
                            <!-- Add supplier options here -->
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Purchase Orders List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة أوامر الشراء ({{ purchase_orders|length }} أمر)
                </h5>
            </div>
            <div class="card-body">
                {% if purchase_orders %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الأمر</th>
                                <th>المورد</th>
                                <th>الحالة</th>
                                <th>عدد الأصناف</th>
                                <th>إجمالي القيمة</th>
                                <th>تاريخ التسليم المتوقع</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in purchase_orders %}
                            <tr>
                                <td>
                                    <strong>{{ order.order_number }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ order.supplier.name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ order.supplier.city }}, {{ order.supplier.country }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if order.status == 'draft' %}
                                    <span class="badge bg-warning text-dark">مسودة</span>
                                    {% elif order.status == 'sent' %}
                                    <span class="badge bg-info">مرسل</span>
                                    {% elif order.status == 'confirmed' %}
                                    <span class="badge bg-success">مؤكد</span>
                                    {% elif order.status == 'partially_received' %}
                                    <span class="badge bg-primary">مستلم جزئياً</span>
                                    {% elif order.status == 'received' %}
                                    <span class="badge bg-success">مستلم</span>
                                    {% elif order.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="fw-bold text-primary">{{ order.items.count }}</span>
                                    <small class="text-muted">صنف</small>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">{{ order.total_amount|floatformat:2 }}</span>
                                    <small class="text-muted">ريال</small>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ order.expected_delivery_date|date:"d/m/Y" }}</strong>
                                        {% if order.expected_delivery_date < today and order.status not in 'received,cancelled' %}
                                        <br><small class="text-danger">متأخر</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ order.created_at|date:"d/m/Y" }}</strong>
                                        <br>
                                        <small class="text-muted">{{ order.created_at|time:"H:i" }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'procurement:purchase_order_detail' order.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if order.status == 'draft' %}
                                        <a href="{% url 'procurement:purchase_order_edit' order.pk %}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{% url 'procurement:print_purchase_order' order.pk %}" 
                                           class="btn btn-outline-secondary" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        {% if order.status == 'draft' %}
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="إجراءات">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item text-info" href="{% url 'procurement:send_purchase_order' order.pk %}">
                                                    <i class="fas fa-paper-plane me-2"></i>إرسال
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'procurement:cancel_purchase_order' order.pk %}">
                                                    <i class="fas fa-times me-2"></i>إلغاء
                                                </a></li>
                                            </ul>
                                        </div>
                                        {% elif order.status == 'sent' %}
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle" 
                                                    data-bs-toggle="dropdown" title="إجراءات">
                                                <i class="fas fa-cog"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item text-success" href="{% url 'procurement:confirm_purchase_order' order.pk %}">
                                                    <i class="fas fa-check me-2"></i>تأكيد
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="{% url 'procurement:cancel_purchase_order' order.pk %}">
                                                    <i class="fas fa-times me-2"></i>إلغاء
                                                </a></li>
                                            </ul>
                                        </div>
                                        {% elif order.status == 'confirmed' %}
                                        <a href="{% url 'procurement:receive_purchase_order' order.pk %}" 
                                           class="btn btn-outline-success btn-sm" title="استلام">
                                            <i class="fas fa-check-double"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أوامر شراء</h5>
                    <p class="text-muted">
                        {% if request.GET.search or request.GET.status or request.GET.supplier %}
                        لم يتم العثور على أوامر تطابق معايير البحث.
                        {% else %}
                        لم يتم إنشاء أي أوامر شراء بعد.
                        {% endif %}
                    </p>
                    {% if not request.GET.search and not request.GET.status and not request.GET.supplier %}
                    <a href="{% url 'procurement:purchase_order_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء أول أمر شراء
                    </a>
                    {% else %}
                    <a href="{% url 'procurement:purchase_order_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        عرض جميع الأوامر
                    </a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on Enter
document.getElementById('search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        this.form.submit();
    }
});

// Auto-submit on status/supplier change
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('supplier').addEventListener('change', function() {
    this.form.submit();
});

// Confirmation for actions
document.addEventListener('click', function(e) {
    if (e.target.closest('a[href*="send"]')) {
        if (!confirm('هل أنت متأكد من إرسال هذا الأمر؟')) {
            e.preventDefault();
        }
    }
    if (e.target.closest('a[href*="confirm"]')) {
        if (!confirm('هل أنت متأكد من تأكيد هذا الأمر؟')) {
            e.preventDefault();
        }
    }
    if (e.target.closest('a[href*="receive"]')) {
        if (!confirm('هل أنت متأكد من استلام هذا الأمر؟')) {
            e.preventDefault();
        }
    }
    if (e.target.closest('a[href*="cancel"]')) {
        if (!confirm('هل أنت متأكد من إلغاء هذا الأمر؟')) {
            e.preventDefault();
        }
    }
});
</script>
{% endblock %}
