{% extends 'base/base.html' %}
{% load static %}

{% block title %}تقرير المخزون - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">تقرير المخزون</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                تقرير المخزون
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقرير
                </h6>
            </div>
            <div class="card-body">
                <form id="reportFilters" class="row g-3">
                    <div class="col-md-3">
                        <label for="warehouse" class="form-label">المستودع</label>
                        <select class="form-select" id="warehouse" name="warehouse">
                            <option value="">جميع المستودعات</option>
                            <option value="1">المستودع الرئيسي</option>
                            <option value="2">مستودع الطوارئ</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="category" class="form-label">التصنيف</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">جميع التصنيفات</option>
                            <option value="1">مضادات حيوية</option>
                            <option value="2">مسكنات</option>
                            <option value="3">أدوية القلب</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">حالة المخزون</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="available">متوفر</option>
                            <option value="low">منخفض</option>
                            <option value="out">نفد</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-sync me-2"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1" id="totalItems">245</h4>
                <small class="text-muted">إجمالي الأصناف</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1" id="availableItems">198</h4>
                <small class="text-muted">متوفر</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1" id="lowStockItems">32</h4>
                <small class="text-muted">مخزون منخفض</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1" id="outOfStockItems">15</h4>
                <small class="text-muted">نفد المخزون</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المخزون حسب الحالة
                </h6>
            </div>
            <div class="card-body">
                <canvas id="stockStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    المخزون حسب التصنيف
                </h6>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Warehouse Breakdown -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    توزيع المخزون حسب المستودعات
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المستودع</th>
                                <th>إجمالي الأصناف</th>
                                <th>متوفر</th>
                                <th>مخزون منخفض</th>
                                <th>نفد المخزون</th>
                                <th>القيمة الإجمالية</th>
                                <th>نسبة الاستغلال</th>
                            </tr>
                        </thead>
                        <tbody id="warehouseBreakdown">
                            <tr>
                                <td>
                                    <div>
                                        <strong>المستودع الرئيسي</strong>
                                        <br>
                                        <small class="text-muted">الطابق الأول - المبنى الرئيسي</small>
                                    </div>
                                </td>
                                <td><span class="fw-bold text-primary">180</span></td>
                                <td><span class="text-success">145</span></td>
                                <td><span class="text-warning">25</span></td>
                                <td><span class="text-danger">10</span></td>
                                <td><span class="text-success fw-bold">2,450,000 ريال</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 75%">75%</div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div>
                                        <strong>مستودع الطوارئ</strong>
                                        <br>
                                        <small class="text-muted">الطابق الثاني - قسم الطوارئ</small>
                                    </div>
                                </td>
                                <td><span class="fw-bold text-primary">65</span></td>
                                <td><span class="text-success">53</span></td>
                                <td><span class="text-warning">7</span></td>
                                <td><span class="text-danger">5</span></td>
                                <td><span class="text-success fw-bold">890,000 ريال</span></td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: 60%">60%</div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Categories -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tags me-2"></i>
                    أعلى التصنيفات قيمة
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">مضادات حيوية</h6>
                            <small class="text-muted">85 صنف</small>
                        </div>
                        <div class="text-end">
                            <span class="text-success fw-bold">1,250,000 ريال</span>
                            <br>
                            <small class="text-muted">37.5%</small>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">أدوية القلب</h6>
                            <small class="text-muted">42 صنف</small>
                        </div>
                        <div class="text-end">
                            <span class="text-success fw-bold">890,000 ريال</span>
                            <br>
                            <small class="text-muted">26.7%</small>
                        </div>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                        <div>
                            <h6 class="mb-1">مسكنات</h6>
                            <small class="text-muted">38 صنف</small>
                        </div>
                        <div class="text-end">
                            <span class="text-success fw-bold">520,000 ريال</span>
                            <br>
                            <small class="text-muted">15.6%</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تنبيهات المخزون
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <i class="fas fa-times-circle me-2"></i>
                        نفد المخزون (15 صنف)
                    </h6>
                    <ul class="mb-0 small">
                        <li>أموكسيسيلين 500 مجم</li>
                        <li>بانادول 500 مجم</li>
                        <li>أسبرين 100 مجم</li>
                    </ul>
                </div>
                <div class="alert alert-warning">
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        مخزون منخفض (32 صنف)
                    </h6>
                    <ul class="mb-0 small">
                        <li>جلوكوفاج 850 مجم (5 علب متبقية)</li>
                        <li>نورفاسك 5 مجم (8 علب متبقية)</li>
                        <li>ليبيتور 20 مجم (3 علب متبقية)</li>
                    </ul>
                </div>
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-clock me-2"></i>
                        تنتهي قريباً (18 صنف)
                    </h6>
                    <p class="mb-0 small">
                        أدوية تنتهي صلاحيتها خلال 30 يوم القادمة
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Inventory Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل المخزون
                </h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="toggleView('summary')">
                        ملخص
                    </button>
                    <button type="button" class="btn btn-outline-primary active" onclick="toggleView('detailed')">
                        تفصيلي
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="inventoryTable">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                                <th>تاريخ انتهاء الصلاحية</th>
                                <th>القيمة</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <!-- Data will be loaded via JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}

.progress {
    background-color: #e9ecef;
}

.alert {
    border-left: 4px solid;
}

.alert-danger {
    border-left-color: #dc3545;
}

.alert-warning {
    border-left-color: #ffc107;
}

.alert-info {
    border-left-color: #0dcaf0;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Sample data - in real implementation, this would come from the backend
const inventoryData = [
    {
        drug: 'أموكسيسيلين 500 مجم',
        warehouse: 'المستودع الرئيسي',
        available: 0,
        minimum: 10,
        status: 'out',
        expiry: '2024-06-15',
        value: 0
    },
    {
        drug: 'بانادول 500 مجم',
        warehouse: 'المستودع الرئيسي',
        available: 5,
        minimum: 20,
        status: 'low',
        expiry: '2024-08-20',
        value: 150
    },
    {
        drug: 'جلوكوفاج 850 مجم',
        warehouse: 'مستودع الطوارئ',
        available: 25,
        minimum: 15,
        status: 'available',
        expiry: '2024-12-10',
        value: 750
    }
];

// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadInventoryTable();
});

function initializeCharts() {
    // Stock Status Pie Chart
    const statusCtx = document.getElementById('stockStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'pie',
        data: {
            labels: ['متوفر', 'مخزون منخفض', 'نفد المخزون'],
            datasets: [{
                data: [198, 32, 15],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Category Bar Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'bar',
        data: {
            labels: ['مضادات حيوية', 'أدوية القلب', 'مسكنات', 'السكري', 'الضغط'],
            datasets: [{
                label: 'عدد الأصناف',
                data: [85, 42, 38, 35, 28],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadInventoryTable() {
    const tbody = document.getElementById('inventoryTableBody');
    tbody.innerHTML = '';

    inventoryData.forEach(item => {
        const row = document.createElement('tr');
        
        let statusBadge = '';
        if (item.status === 'out') {
            statusBadge = '<span class="badge bg-danger">نفد المخزون</span>';
        } else if (item.status === 'low') {
            statusBadge = '<span class="badge bg-warning text-dark">مخزون منخفض</span>';
        } else {
            statusBadge = '<span class="badge bg-success">متوفر</span>';
        }

        row.innerHTML = `
            <td>${item.drug}</td>
            <td>${item.warehouse}</td>
            <td class="${item.status === 'out' ? 'text-danger' : item.status === 'low' ? 'text-warning' : 'text-success'} fw-bold">
                ${item.available}
            </td>
            <td>${item.minimum}</td>
            <td>${statusBadge}</td>
            <td>${item.expiry}</td>
            <td class="text-success">${item.value} ريال</td>
        `;
        
        tbody.appendChild(row);
    });
}

function generateReport() {
    // Show loading
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    button.disabled = true;

    // Simulate API call
    setTimeout(() => {
        // Update summary cards with new data
        document.getElementById('totalItems').textContent = Math.floor(Math.random() * 300) + 200;
        document.getElementById('availableItems').textContent = Math.floor(Math.random() * 200) + 150;
        document.getElementById('lowStockItems').textContent = Math.floor(Math.random() * 50) + 20;
        document.getElementById('outOfStockItems').textContent = Math.floor(Math.random() * 20) + 10;

        // Reload table
        loadInventoryTable();

        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;

        // Show success message
        showToast('تم تحديث التقرير بنجاح', 'success');
    }, 2000);
}

function toggleView(viewType) {
    const buttons = document.querySelectorAll('.btn-group .btn');
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    if (viewType === 'summary') {
        // Show summary view
        showToast('عرض الملخص', 'info');
    } else {
        // Show detailed view
        showToast('عرض التفاصيل', 'info');
    }
}

function exportToExcel() {
    showToast('جاري تصدير التقرير إلى Excel...', 'info');
    // Implementation for Excel export
}

function exportToPDF() {
    showToast('جاري تصدير التقرير إلى PDF...', 'info');
    // Implementation for PDF export
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
