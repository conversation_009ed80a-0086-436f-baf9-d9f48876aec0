from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    # Inventory URLs
    path('', views.InventoryListView.as_view(), name='inventory_list'),
    path('item/<int:pk>/', views.InventoryItemDetailView.as_view(), name='inventory_item_detail'),
    path('item/<int:pk>/edit/', views.InventoryItemUpdateView.as_view(), name='inventory_item_edit'),
    
    # Warehouse URLs
    path('warehouses/', views.WarehouseListView.as_view(), name='warehouse_list'),
    path('warehouses/create/', views.WarehouseCreateView.as_view(), name='warehouse_create'),
    path('warehouses/<int:pk>/', views.WarehouseDetailView.as_view(), name='warehouse_detail'),
    path('warehouses/<int:pk>/edit/', views.WarehouseUpdateView.as_view(), name='warehouse_edit'),
    path('warehouses/<int:pk>/delete/', views.WarehouseDeleteView.as_view(), name='warehouse_delete'),
    
    # Batch URLs
    path('batches/', views.BatchListView.as_view(), name='batch_list'),
    path('batches/create/', views.BatchCreateView.as_view(), name='batch_create'),
    path('batches/<int:pk>/', views.BatchDetailView.as_view(), name='batch_detail'),
    path('batches/<int:pk>/edit/', views.BatchUpdateView.as_view(), name='batch_edit'),
    path('batches/<int:pk>/delete/', views.BatchDeleteView.as_view(), name='batch_delete'),
    
    # Movement URLs
    path('movements/', views.MovementListView.as_view(), name='movement_list'),
    path('movements/create/', views.MovementCreateView.as_view(), name='movement_create'),
    path('movements/<int:pk>/', views.MovementDetailView.as_view(), name='movement_detail'),
    
    # Stock Status URLs
    path('low-stock/', views.LowStockView.as_view(), name='low_stock'),
    path('expired/', views.ExpiredDrugsView.as_view(), name='expired_drugs'),
    path('expiring-soon/', views.ExpiringSoonView.as_view(), name='expiring_soon'),
    
    # Adjustment URLs
    path('adjustment/', views.InventoryAdjustmentView.as_view(), name='inventory_adjustment'),
    path('transfer/', views.InventoryTransferView.as_view(), name='inventory_transfer'),
    
    # API URLs
    path('api/stock-check/', views.stock_check_api, name='stock_check_api'),
    path('api/warehouse/<int:warehouse_id>/drugs/', views.warehouse_drugs_api, name='warehouse_drugs_api'),
]
