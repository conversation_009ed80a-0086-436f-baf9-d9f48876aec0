from django import forms
from .models import Drug, DrugCategory, Manufacturer

class DrugForm(forms.ModelForm):
    class Meta:
        model = Drug
        fields = [
            'scientific_name', 'commercial_name', 'barcode',
            'category', 'manufacturer', 'unit', 'unit_price',
            'strength', 'dosage_form', 'description',
            'is_active', 'requires_prescription', 'is_controlled'
        ]
        widgets = {
            'scientific_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل الاسم العلمي للدواء'
            }),
            'commercial_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل الاسم التجاري للدواء'
            }),
            'barcode': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل الباركود (اختياري)'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'manufacturer': forms.Select(attrs={
                'class': 'form-select'
            }),
            'unit': forms.Select(attrs={
                'class': 'form-select'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0.01',
                'placeholder': 'أدخل سعر الوحدة'
            }),
            'strength': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل التركيز (مثل: 500mg)'
            }),
            'dosage_form': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل الشكل الصيدلاني (مثل: أقراص)'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل وصف الدواء (اختياري)'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'requires_prescription': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_controlled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Filter active manufacturers
        self.fields['manufacturer'].queryset = Manufacturer.objects.filter(is_active=True)
        
        # Set required fields
        self.fields['scientific_name'].required = True
        self.fields['commercial_name'].required = True
        self.fields['category'].required = True
        self.fields['manufacturer'].required = True
        self.fields['unit'].required = True
        self.fields['unit_price'].required = True

    def clean_barcode(self):
        barcode = self.cleaned_data.get('barcode')
        if barcode:
            # Check if barcode already exists (excluding current instance)
            existing = Drug.objects.filter(barcode=barcode)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('هذا الباركود مستخدم بالفعل.')
        return barcode

    def clean_unit_price(self):
        unit_price = self.cleaned_data.get('unit_price')
        if unit_price and unit_price <= 0:
            raise forms.ValidationError('يجب أن يكون سعر الوحدة أكبر من صفر.')
        return unit_price

class DrugCategoryForm(forms.ModelForm):
    class Meta:
        model = DrugCategory
        fields = ['name', 'description']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم التصنيف'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل وصف التصنيف (اختياري)'
            }),
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check if category name already exists (excluding current instance)
            existing = DrugCategory.objects.filter(name__iexact=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('تصنيف بهذا الاسم موجود بالفعل.')
        return name

class ManufacturerForm(forms.ModelForm):
    class Meta:
        model = Manufacturer
        fields = ['name', 'country', 'contact_info', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل اسم الشركة المصنعة'
            }),
            'country': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'أدخل البلد'
            }),
            'contact_info': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'أدخل معلومات الاتصال (اختياري)'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # Check if manufacturer name already exists (excluding current instance)
            existing = Manufacturer.objects.filter(name__iexact=name)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError('شركة مصنعة بهذا الاسم موجودة بالفعل.')
        return name

class DrugSearchForm(forms.Form):
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث في الأدوية...',
            'autocomplete': 'off'
        })
    )
    category = forms.ModelChoiceField(
        queryset=DrugCategory.objects.all(),
        required=False,
        empty_label="جميع التصنيفات",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    manufacturer = forms.ModelChoiceField(
        queryset=Manufacturer.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الشركات المصنعة",
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    unit = forms.ChoiceField(
        choices=[('', 'جميع الوحدات')] + Drug.UNIT_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    requires_prescription = forms.ChoiceField(
        choices=[
            ('', 'الكل'),
            ('true', 'يتطلب وصفة طبية'),
            ('false', 'لا يتطلب وصفة طبية')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
    is_controlled = forms.ChoiceField(
        choices=[
            ('', 'الكل'),
            ('true', 'دواء مراقب'),
            ('false', 'دواء غير مراقب')
        ],
        required=False,
        widget=forms.Select(attrs={
            'class': 'form-select'
        })
    )
