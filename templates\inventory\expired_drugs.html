{% extends 'base/base.html' %}
{% load static %}

{% block title %}الأدوية المنتهية الصلاحية - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item active">الأدوية المنتهية الصلاحية</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-clock text-danger me-2"></i>
                الأدوية المنتهية الصلاحية
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:expiring_soon' %}" class="btn btn-warning">
                    <i class="fas fa-calendar-alt me-2"></i>
                    تنتهي قريباً
                </a>
                <a href="{% url 'inventory:batch_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    جميع الدفعات
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Alert Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-danger" role="alert">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تحذير: أدوية منتهية الصلاحية
            </h5>
            <p class="mb-0">
                يوجد <strong>{{ expired_batches|length }}</strong> دفعة دواء منتهية الصلاحية.
                يجب إزالة هذه الأدوية من المخزون فوراً وفقاً للإجراءات المعتمدة للتخلص الآمن.
            </p>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">{{ expired_batches|length }}</h4>
                <small class="text-muted">دفعة منتهية</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with total_quantity=0 %}
                        {% for batch in expired_batches %}
                            {% for item in batch.inventory_items.all %}
                                {% with total_quantity=total_quantity|add:item.quantity %}{% endwith %}
                            {% endfor %}
                        {% endfor %}
                        {{ total_quantity }}
                    {% endwith %}
                </h4>
                <small class="text-muted">وحدة منتهية</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% regroup expired_batches by drug as drug_groups %}
                    {{ drug_groups|length }}
                </h4>
                <small class="text-muted">دواء متأثر</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-secondary">
            <div class="card-body">
                <h4 class="text-secondary mb-1">
                    تقديرية
                </h4>
                <small class="text-muted">ريال خسارة تقديرية</small>
            </div>
        </div>
    </div>
</div>

<!-- Expired Batches -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الدفعات المنتهية الصلاحية
                </h5>
            </div>
            <div class="card-body">
                {% if expired_batches %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>رقم الدفعة</th>
                                <th>تاريخ الانتهاء</th>
                                <th>منتهي منذ</th>
                                <th>الكمية المتبقية</th>
                                <th>سعر التكلفة</th>
                                <th>الخسارة المقدرة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for batch in expired_batches %}
                            <tr class="table-danger">
                                <td>
                                    <div>
                                        <strong>{{ batch.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ batch.drug.scientific_name }}</small>
                                        <br>
                                        <span class="badge bg-primary">{{ batch.drug.category.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <code>{{ batch.batch_number }}</code>
                                    {% if batch.supplier_batch_number %}
                                    <br><small class="text-muted">مورد: {{ batch.supplier_batch_number }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-danger fw-bold">{{ batch.expiry_date|date:"d/m/Y" }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-danger">
                                        منتهي
                                    </span>
                                </td>
                                <td>
                                    {% with total_qty=0 %}
                                        {% for item in batch.inventory_items.all %}
                                            {% with total_qty=total_qty|add:item.quantity %}{% endwith %}
                                        {% endfor %}
                                        <span class="fw-bold text-danger">{{ total_qty }}</span>
                                        <small class="text-muted">{{ batch.drug.get_unit_display }}</small>
                                        {% if batch.inventory_items.count > 1 %}
                                        <br><small class="text-muted">في {{ batch.inventory_items.count }} مستودع</small>
                                        {% endif %}
                                    {% endwith %}
                                </td>
                                <td class="text-success">{{ batch.cost_price }} ريال</td>
                                <td>
                                    <span class="text-danger fw-bold">{{ batch.cost_price }} ريال</span>
                                    <br><small class="text-muted">تقديرية</small>
                                </td>
                                <td>
                                    {% if batch.is_recalled %}
                                    <span class="badge bg-dark">مسحوب</span>
                                    {% else %}
                                    <span class="badge bg-danger">يحتاج إزالة</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        {% if not batch.is_recalled %}
                                        <button type="button" class="btn btn-danger"
                                                onclick="removeExpiredBatch({{ batch.pk }})" title="إزالة من المخزون">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                        <a href="{% url 'inventory:batch_detail' batch.pk %}"
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-warning"
                                                onclick="showBatchLocations({{ batch.pk }})" title="عرض المواقع">
                                            <i class="fas fa-map-marker-alt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Action Buttons -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">إجراءات سريعة:</h6>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-danger" onclick="bulkRemoveExpired()">
                                        <i class="fas fa-trash me-2"></i>
                                        إزالة جميع المنتهية
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="generateDisposalReport()">
                                        <i class="fas fa-file-alt me-2"></i>
                                        تقرير التخلص
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="exportExpiredReport()">
                                        <i class="fas fa-file-export me-2"></i>
                                        تصدير التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5 class="text-success">ممتاز! لا توجد أدوية منتهية الصلاحية</h5>
                    <p class="text-muted">جميع دفعات الأدوية في المخزون صالحة للاستخدام.</p>
                    <a href="{% url 'inventory:expiring_soon' %}" class="btn btn-warning">
                        <i class="fas fa-calendar-alt me-2"></i>
                        فحص الأدوية التي تنتهي قريباً
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Disposal Guidelines -->
{% if expired_batches %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات التخلص الآمن
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>خطوات التخلص:</h6>
                        <ol class="small">
                            <li>فصل الأدوية المنتهية عن الصالحة</li>
                            <li>توثيق جميع الأدوية المراد التخلص منها</li>
                            <li>التواصل مع الجهة المختصة للتخلص الآمن</li>
                            <li>الحصول على شهادة التخلص</li>
                            <li>تحديث سجلات المخزون</li>
                        </ol>
                    </div>
                    <div class="col-md-6">
                        <h6>احتياطات مهمة:</h6>
                        <ul class="small">
                            <li>عدم التخلص من الأدوية في القمامة العادية</li>
                            <li>عدم سكب الأدوية السائلة في المجاري</li>
                            <li>ارتداء معدات الحماية الشخصية</li>
                            <li>اتباع البروتوكولات البيئية</li>
                            <li>الاحتفاظ بسجلات التخلص</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function removeExpiredBatch(batchId) {
    if (confirm('هل أنت متأكد من إزالة هذه الدفعة المنتهية من المخزون؟')) {
        // Here you would make an AJAX call to remove the batch
        fetch(`/inventory/batches/${batchId}/remove-expired/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('حدث خطأ أثناء إزالة الدفعة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إزالة الدفعة');
        });
    }
}

function showBatchLocations(batchId) {
    alert('سيتم تطوير هذه الميزة قريباً - عرض مواقع الدفعة في المستودعات');
}

function bulkRemoveExpired() {
    if (confirm('هل أنت متأكد من إزالة جميع الدفعات المنتهية الصلاحية؟')) {
        alert('سيتم تطوير هذه الميزة قريباً - إزالة جميع الدفعات المنتهية');
    }
}

function generateDisposalReport() {
    alert('سيتم تطوير هذه الميزة قريباً - إنشاء تقرير التخلص الآمن');
}

function exportExpiredReport() {
    alert('سيتم تطوير هذه الميزة قريباً - تصدير تقرير الأدوية المنتهية');
}

// Add CSRF token as meta tag
document.addEventListener('DOMContentLoaded', function() {
    if (!document.querySelector('meta[name="csrf-token"]')) {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            const meta = document.createElement('meta');
            meta.name = 'csrf-token';
            meta.content = csrfToken.value;
            document.head.appendChild(meta);
        }
    }
});
</script>
{% endblock %}
