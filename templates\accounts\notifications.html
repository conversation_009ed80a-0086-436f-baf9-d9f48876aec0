{% extends 'base/base.html' %}
{% load static %}

{% block title %}التنبيهات - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">التنبيهات</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-bell me-2"></i>
                التنبيهات والإشعارات
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="markAllAsRead()">
                    <i class="fas fa-check-double me-2"></i>
                    تحديد الكل كمقروء
                </button>
                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-filter me-2"></i>
                    فلترة
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?filter=unread">غير مقروءة فقط</a></li>
                    <li><a class="dropdown-item" href="?filter=read">مقروءة فقط</a></li>
                    <li><a class="dropdown-item" href="?filter=urgent">عاجلة فقط</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{% url 'accounts:notifications' %}">جميع التنبيهات</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Notifications Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ notifications|length }}</h4>
                <small class="text-muted">إجمالي التنبيهات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with unread_count=0 %}
                        {% for notification in notifications %}
                            {% if not notification.is_read %}
                                {% with unread_count=unread_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ unread_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">غير مقروءة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with urgent_count=0 %}
                        {% for notification in notifications %}
                            {% if notification.priority == 'urgent' %}
                                {% with urgent_count=urgent_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ urgent_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">عاجلة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with today_count=0 %}
                        {% for notification in notifications %}
                            {% if notification.created_at|date:"Y-m-d" == "now"|date:"Y-m-d" %}
                                {% with today_count=today_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ today_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">اليوم</small>
            </div>
        </div>
    </div>
</div>

<!-- Notifications List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة التنبيهات
                </h5>
            </div>
            <div class="card-body">
                {% if notifications %}
                <div class="list-group list-group-flush">
                    {% for notification in notifications %}
                    <div class="list-group-item {% if not notification.is_read %}bg-light border-start border-primary border-3{% endif %}" 
                         data-notification-id="{{ notification.id }}">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <!-- Notification Icon -->
                                    <div class="me-3">
                                        {% if notification.notification_type == 'low_stock' %}
                                        <i class="fas fa-exclamation-triangle text-warning fa-lg"></i>
                                        {% elif notification.notification_type == 'expiry_warning' %}
                                        <i class="fas fa-clock text-danger fa-lg"></i>
                                        {% elif notification.notification_type == 'order_approval' %}
                                        <i class="fas fa-check-circle text-success fa-lg"></i>
                                        {% elif notification.notification_type == 'order_rejection' %}
                                        <i class="fas fa-times-circle text-danger fa-lg"></i>
                                        {% elif notification.notification_type == 'new_order' %}
                                        <i class="fas fa-shopping-cart text-info fa-lg"></i>
                                        {% elif notification.notification_type == 'delivery_reminder' %}
                                        <i class="fas fa-truck text-primary fa-lg"></i>
                                        {% else %}
                                        <i class="fas fa-bell text-secondary fa-lg"></i>
                                        {% endif %}
                                    </div>
                                    
                                    <!-- Notification Content -->
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 {% if not notification.is_read %}fw-bold{% endif %}">
                                            {{ notification.title }}
                                        </h6>
                                        <p class="mb-1 text-muted">{{ notification.message }}</p>
                                        
                                        <!-- Notification Meta -->
                                        <div class="d-flex align-items-center">
                                            <small class="text-muted me-3">
                                                <i class="fas fa-clock me-1"></i>
                                                {{ notification.created_at|timesince }} مضت
                                            </small>
                                            
                                            <!-- Priority Badge -->
                                            {% if notification.priority == 'urgent' %}
                                            <span class="badge bg-danger me-2">عاجل</span>
                                            {% elif notification.priority == 'high' %}
                                            <span class="badge bg-warning text-dark me-2">عالي</span>
                                            {% elif notification.priority == 'normal' %}
                                            <span class="badge bg-info me-2">عادي</span>
                                            {% else %}
                                            <span class="badge bg-secondary me-2">منخفض</span>
                                            {% endif %}
                                            
                                            <!-- Type Badge -->
                                            <span class="badge bg-light text-dark">{{ notification.get_notification_type_display }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="ms-3">
                                <div class="btn-group-vertical btn-group-sm" role="group">
                                    {% if not notification.is_read %}
                                    <button type="button" class="btn btn-outline-primary" 
                                            onclick="markAsRead({{ notification.id }})" title="تحديد كمقروء">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                    
                                    {% if notification.action_url %}
                                    <a href="{{ notification.action_url }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {% endif %}
                                    
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="deleteNotification({{ notification.id }})" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تنبيهات</h5>
                    <p class="text-muted">لم يتم استلام أي تنبيهات بعد.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Mark single notification as read
function markAsRead(notificationId) {
    fetch(`/accounts/notifications/${notificationId}/read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update UI
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.classList.remove('bg-light', 'border-start', 'border-primary', 'border-3');
            
            // Remove the mark as read button
            const markButton = notificationElement.querySelector('button[onclick*="markAsRead"]');
            if (markButton) {
                markButton.remove();
            }
            
            // Update counters
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث التنبيه');
    });
}

// Mark all notifications as read
function markAllAsRead() {
    if (confirm('هل تريد تحديد جميع التنبيهات كمقروءة؟')) {
        fetch('/accounts/notifications/mark-all-read/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحديث التنبيهات');
        });
    }
}

// Delete notification
function deleteNotification(notificationId) {
    if (confirm('هل تريد حذف هذا التنبيه؟')) {
        // Implementation for deleting notification
        console.log('Delete notification:', notificationId);
        // You would implement the actual delete functionality here
    }
}

// Add CSRF token to all requests
document.addEventListener('DOMContentLoaded', function() {
    // Add CSRF token as a meta tag for easier access
    if (!document.querySelector('meta[name="csrf-token"]')) {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            const meta = document.createElement('meta');
            meta.name = 'csrf-token';
            meta.content = csrfToken.value;
            document.head.appendChild(meta);
        }
    }
});
</script>
{% endblock %}
