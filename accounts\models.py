from django.db import models
from django.contrib.auth.models import User
from institutions.models import Institution, Department

class UserProfile(models.Model):
    """ملف المستخدم الشخصي"""
    USER_ROLES = [
        ('admin', 'مدير النظام'),
        ('pharmacist', 'صيدلي'),
        ('warehouse_manager', 'مدير مستودع'),
        ('department_head', 'رئيس قسم'),
        ('procurement_officer', 'مسؤول مشتريات'),
        ('inventory_clerk', 'موظف مخزون'),
        ('viewer', 'مشاهد'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name="المستخدم")
    role = models.CharField(max_length=20, choices=USER_ROLES, verbose_name="الدور")

    # معلومات شخصية
    employee_id = models.CharField(max_length=50, unique=True, verbose_name="رقم الموظف")
    phone = models.CharField(max_length=20, blank=True, verbose_name="الهاتف")
    department = models.ForeignKey(Department, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="القسم")
    institution = models.ForeignKey(Institution, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="المؤسسة")

    # صورة المستخدم
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name="الصورة الشخصية")

    # حالة المستخدم
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "ملف المستخدم"
        verbose_name_plural = "ملفات المستخدمين"
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.get_full_name()} ({self.get_role_display()})"

    @property
    def full_name(self):
        return self.user.get_full_name() or self.user.username

class Permission(models.Model):
    """الصلاحيات المخصصة"""
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم الصلاحية")
    codename = models.CharField(max_length=50, unique=True, verbose_name="الرمز")
    description = models.TextField(blank=True, verbose_name="الوصف")

    class Meta:
        verbose_name = "الصلاحية"
        verbose_name_plural = "الصلاحيات"
        ordering = ['name']

    def __str__(self):
        return self.name

class Role(models.Model):
    """الأدوار"""
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم الدور")
    description = models.TextField(blank=True, verbose_name="الوصف")
    permissions = models.ManyToManyField(Permission, blank=True, verbose_name="الصلاحيات")

    # حالة الدور
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "الدور"
        verbose_name_plural = "الأدوار"
        ordering = ['name']

    def __str__(self):
        return self.name

class UserRole(models.Model):
    """أدوار المستخدمين"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles', verbose_name="المستخدم")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, verbose_name="الدور")

    # نطاق الصلاحية
    institution = models.ForeignKey(Institution, on_delete=models.CASCADE, null=True, blank=True, verbose_name="المؤسسة")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, null=True, blank=True, verbose_name="القسم")

    # حالة الدور
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    # تواريخ
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التعيين")
    assigned_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='assigned_roles', verbose_name="عين بواسطة")

    class Meta:
        verbose_name = "دور المستخدم"
        verbose_name_plural = "أدوار المستخدمين"
        unique_together = ['user', 'role', 'institution', 'department']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role.name}"
