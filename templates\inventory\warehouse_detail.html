{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ warehouse.name }} - تفاصيل المستودع{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:warehouse_list' %}">المستودعات</a></li>
        <li class="breadcrumb-item active">{{ warehouse.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-building me-2"></i>
                {{ warehouse.name }}
                {% if warehouse.is_main_warehouse %}
                <span class="badge bg-warning text-dark ms-2">رئيسي</span>
                {% endif %}
                {% if not warehouse.is_active %}
                <span class="badge bg-secondary ms-2">غير نشط</span>
                {% endif %}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:warehouse_edit' warehouse.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <a href="{% url 'inventory:warehouse_delete' warehouse.pk %}" class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من حذف هذا المستودع؟')">
                    <i class="fas fa-trash me-2"></i>
                    حذف
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Warehouse Information -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المستودع
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-building"></i>
                    </div>
                    <h4 class="text-primary">{{ warehouse.name }}</h4>
                    <span class="badge bg-info">{{ warehouse.code }}</span>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted small">الوصف</label>
                    {% if warehouse.description %}
                    <p class="mb-0">{{ warehouse.description }}</p>
                    {% else %}
                    <p class="text-muted mb-0">لا يوجد وصف لهذا المستودع</p>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted small">الموقع</label>
                    <p class="mb-0">{{ warehouse.location }}</p>
                </div>
                
                {% if warehouse.capacity %}
                <div class="mb-3">
                    <label class="form-label text-muted small">السعة التخزينية</label>
                    <p class="mb-0 fw-bold text-info">{{ warehouse.capacity }}</p>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label text-muted small">نوع المستودع</label>
                    <div>
                        {% if warehouse.is_main_warehouse %}
                        <span class="badge bg-warning text-dark">مستودع رئيسي</span>
                        {% endif %}
                        {% if warehouse.temperature_controlled %}
                        <span class="badge bg-info">متحكم في الحرارة</span>
                        {% endif %}
                        {% if warehouse.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary mb-1">{{ warehouse.inventory_items.count }}</h4>
                        <small class="text-muted">عنصر مخزون</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">
                            {% with active_items=warehouse.inventory_items.filter.is_active.count %}
                            {{ active_items|default:warehouse.inventory_items.count }}
                            {% endwith %}
                        </h4>
                        <small class="text-muted">عنصر نشط</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <div class="d-flex justify-content-between mb-2">
                        <span>تاريخ الإنشاء:</span>
                        <span>{{ warehouse.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>آخر تحديث:</span>
                        <span>{{ warehouse.updated_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>رقم المستودع:</span>
                        <span><code>#{{ warehouse.id }}</code></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-address-book me-2"></i>
                    معلومات الاتصال
                </h6>
            </div>
            <div class="card-body">
                {% if warehouse.manager %}
                <div class="mb-3">
                    <label class="form-label text-muted small">مدير المستودع</label>
                    <p class="fw-bold">{{ warehouse.manager }}</p>
                </div>
                {% endif %}
                
                {% if warehouse.phone %}
                <div class="mb-3">
                    <label class="form-label text-muted small">الهاتف</label>
                    <p class="fw-bold">
                        <a href="tel:{{ warehouse.phone }}" class="text-decoration-none">
                            <i class="fas fa-phone me-1"></i>
                            {{ warehouse.phone }}
                        </a>
                    </p>
                </div>
                {% endif %}
                
                {% if warehouse.email %}
                <div class="mb-3">
                    <label class="form-label text-muted small">البريد الإلكتروني</label>
                    <p class="fw-bold">
                        <a href="mailto:{{ warehouse.email }}" class="text-decoration-none">
                            <i class="fas fa-envelope me-1"></i>
                            {{ warehouse.email }}
                        </a>
                    </p>
                </div>
                {% endif %}
                
                {% if not warehouse.manager and not warehouse.phone and not warehouse.email %}
                <div class="text-center py-3">
                    <i class="fas fa-info-circle text-muted me-2"></i>
                    <span class="text-muted">لا توجد معلومات اتصال</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'inventory:inventory_list' %}?warehouse={{ warehouse.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-warehouse me-2"></i>
                        عرض مخزون المستودع
                    </a>
                    <a href="{% url 'inventory:movement_create' %}?warehouse={{ warehouse.id }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        إضافة حركة مخزون
                    </a>
                    <a href="{% url 'inventory:warehouse_edit' warehouse.pk %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المستودع
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-chart-line me-2"></i>
                        تقرير المستودع
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Inventory Items in this Warehouse -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    عناصر المخزون ({{ warehouse.inventory_items.count }})
                </h5>
                {% if warehouse.inventory_items.exists %}
                <a href="{% url 'inventory:inventory_list' %}?warehouse={{ warehouse.id }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if warehouse.inventory_items.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>الدفعة</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in warehouse.inventory_items.all|slice:":10" %}
                            <tr class="{% if item.is_out_of_stock %}table-danger{% elif item.is_low_stock %}table-warning{% endif %}">
                                <td>
                                    <div>
                                        <strong>{{ item.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.drug.scientific_name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <code>{{ item.batch.batch_number }}</code>
                                    <br>
                                    <small class="text-muted">{{ item.batch.expiry_date|date:"d/m/Y" }}</small>
                                </td>
                                <td>
                                    <span class="fw-bold {% if item.is_out_of_stock %}text-danger{% elif item.is_low_stock %}text-warning{% else %}text-success{% endif %}">
                                        {{ item.available_quantity }}
                                    </span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                </td>
                                <td>{{ item.minimum_stock }}</td>
                                <td>
                                    {% if item.is_out_of_stock %}
                                    <span class="badge bg-danger">نفد المخزون</span>
                                    {% elif item.is_low_stock %}
                                    <span class="badge bg-warning text-dark">مخزون منخفض</span>
                                    {% else %}
                                    <span class="badge bg-success">متوفر</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:inventory_item_detail' item.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:movement_create' %}?item={{ item.pk }}" 
                                           class="btn btn-outline-primary" title="حركة مخزون">
                                            <i class="fas fa-exchange-alt"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if warehouse.inventory_items.count > 10 %}
                <div class="text-center mt-3">
                    <a href="{% url 'inventory:inventory_list' %}?warehouse={{ warehouse.id }}" class="btn btn-primary">
                        عرض جميع العناصر ({{ warehouse.inventory_items.count }})
                    </a>
                </div>
                {% endif %}
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد عناصر مخزون</h5>
                    <p class="text-muted">لم يتم إضافة أي عناصر لهذا المستودع بعد.</p>
                    <a href="{% url 'inventory:batch_create' %}?warehouse={{ warehouse.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول عنصر
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Statistics -->
        {% if warehouse.inventory_items.exists %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات المستودع
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-primary mb-1">{{ warehouse.inventory_items.count }}</h5>
                            <small class="text-muted">إجمالي العناصر</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-warning mb-1">
                                {% with low_stock=0 %}
                                    {% for item in warehouse.inventory_items.all %}
                                        {% if item.is_low_stock %}
                                            {% with low_stock=low_stock|add:1 %}{% endwith %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ low_stock }}
                                {% endwith %}
                            </h5>
                            <small class="text-muted">مخزون منخفض</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-danger mb-1">
                                {% with out_stock=0 %}
                                    {% for item in warehouse.inventory_items.all %}
                                        {% if item.is_out_of_stock %}
                                            {% with out_stock=out_stock|add:1 %}{% endwith %}
                                        {% endif %}
                                    {% endfor %}
                                    {{ out_stock }}
                                {% endwith %}
                            </h5>
                            <small class="text-muted">نفد المخزون</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center mb-3">
                        <div class="border rounded p-3">
                            <h5 class="text-info mb-1">
                                {% regroup warehouse.inventory_items.all by drug.category as categories %}
                                {{ categories|length }}
                            </h5>
                            <small class="text-muted">تصنيف</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
