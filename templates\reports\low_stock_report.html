{% extends 'base/base.html' %}
{% load static %}

{% block title %}تقرير المخزون المنخفض - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">تقرير المخزون المنخفض</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                تقرير المخزون المنخفض
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Alert Summary -->
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning border-left-warning">
            <h4 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>
                تحذير: مخزون منخفض
            </h4>
            <p class="mb-2">
                يوجد <strong>32 صنف دواء</strong> بمخزون منخفض يحتاج إعادة تموين فوري.
                <strong>15 صنف</strong> منها في حالة حرجة (أقل من 25% من الحد الأدنى).
            </p>
            <hr>
            <p class="mb-0">
                <i class="fas fa-info-circle me-2"></i>
                يُنصح بإنشاء أوامر شراء فورية للأصناف الحرجة لتجنب نفاد المخزون.
            </p>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">15</h4>
                <small class="text-muted">حالة حرجة</small>
                <div class="mt-2">
                    <small class="text-danger">أقل من 25%</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">17</h4>
                <small class="text-muted">تحذير</small>
                <div class="mt-2">
                    <small class="text-warning">25% - 50%</small>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">2,450,000</h4>
                <small class="text-muted">قيمة المطلوب (ريال)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">8</h4>
                <small class="text-muted">موردين مطلوبين</small>
            </div>
        </div>
    </div>
</div>

<!-- Priority Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-fire me-2"></i>
                    إجراءات فورية مطلوبة - حالة حرجة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>النقص</th>
                                <th>المورد المفضل</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-danger">
                                <td>
                                    <div>
                                        <strong>أموكسيسيلين 500 مجم</strong>
                                        <br>
                                        <small class="text-muted">مضاد حيوي</small>
                                    </div>
                                </td>
                                <td>المستودع الرئيسي</td>
                                <td><span class="text-danger fw-bold">2</span></td>
                                <td>50</td>
                                <td><span class="text-danger fw-bold">48</span></td>
                                <td>شركة الدواء السعودية</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="createUrgentOrder('amoxicillin')">
                                        <i class="fas fa-shopping-cart me-1"></i>
                                        طلب فوري
                                    </button>
                                </td>
                            </tr>
                            <tr class="table-danger">
                                <td>
                                    <div>
                                        <strong>بانادول 500 مجم</strong>
                                        <br>
                                        <small class="text-muted">مسكن</small>
                                    </div>
                                </td>
                                <td>مستودع الطوارئ</td>
                                <td><span class="text-danger fw-bold">5</span></td>
                                <td>30</td>
                                <td><span class="text-danger fw-bold">25</span></td>
                                <td>شركة فايزر</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="createUrgentOrder('panadol')">
                                        <i class="fas fa-shopping-cart me-1"></i>
                                        طلب فوري
                                    </button>
                                </td>
                            </tr>
                            <tr class="table-danger">
                                <td>
                                    <div>
                                        <strong>أنسولين سريع المفعول</strong>
                                        <br>
                                        <small class="text-muted">أدوية السكري</small>
                                    </div>
                                </td>
                                <td>المستودع الرئيسي</td>
                                <td><span class="text-danger fw-bold">3</span></td>
                                <td>20</td>
                                <td><span class="text-danger fw-bold">17</span></td>
                                <td>شركة نوفو نورديسك</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="createUrgentOrder('insulin')">
                                        <i class="fas fa-shopping-cart me-1"></i>
                                        طلب فوري
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Warning Level Items -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    مستوى تحذير - يحتاج متابعة
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الكمية المتاحة</th>
                                <th>الحد الأدنى</th>
                                <th>النسبة المتبقية</th>
                                <th>التوقع</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-warning">
                                <td>
                                    <div>
                                        <strong>جلوكوفاج 850 مجم</strong>
                                        <br>
                                        <small class="text-muted">أدوية السكري</small>
                                    </div>
                                </td>
                                <td>المستودع الرئيسي</td>
                                <td><span class="text-warning fw-bold">15</span></td>
                                <td>40</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-warning" style="width: 37.5%">37.5%</div>
                                    </div>
                                </td>
                                <td><small class="text-muted">نفاد خلال 10 أيام</small></td>
                                <td>
                                    <button class="btn btn-sm btn-warning" onclick="planOrder('glucophage')">
                                        <i class="fas fa-calendar me-1"></i>
                                        جدولة طلب
                                    </button>
                                </td>
                            </tr>
                            <tr class="table-warning">
                                <td>
                                    <div>
                                        <strong>نورفاسك 5 مجم</strong>
                                        <br>
                                        <small class="text-muted">أدوية الضغط</small>
                                    </div>
                                </td>
                                <td>المستودع الرئيسي</td>
                                <td><span class="text-warning fw-bold">12</span></td>
                                <td>25</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-warning" style="width: 48%">48%</div>
                                    </div>
                                </td>
                                <td><small class="text-muted">نفاد خلال 8 أيام</small></td>
                                <td>
                                    <button class="btn btn-sm btn-warning" onclick="planOrder('norvasc')">
                                        <i class="fas fa-calendar me-1"></i>
                                        جدولة طلب
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المخزون المنخفض حسب التصنيف
                </h6>
            </div>
            <div class="card-body">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    المخزون المنخفض حسب المستودع
                </h6>
            </div>
            <div class="card-body">
                <canvas id="warehouseChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recommended Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    الإجراءات الموصى بها
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="mb-0">إجراءات فورية</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2">
                                        <i class="fas fa-shopping-cart text-danger me-2"></i>
                                        إنشاء 3 أوامر شراء عاجلة
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-phone text-danger me-2"></i>
                                        الاتصال بالموردين المفضلين
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-truck text-danger me-2"></i>
                                        طلب تسليم سريع (24-48 ساعة)
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">خلال أسبوع</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2">
                                        <i class="fas fa-calendar text-warning me-2"></i>
                                        جدولة 5 أوامر شراء
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-chart-line text-warning me-2"></i>
                                        مراجعة الحدود الدنيا
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-sync text-warning me-2"></i>
                                        تحديث توقعات الاستهلاك
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0">تحسينات مستقبلية</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li class="mb-2">
                                        <i class="fas fa-robot text-info me-2"></i>
                                        تفعيل الطلب التلقائي
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-bell text-info me-2"></i>
                                        تحسين نظام التنبيهات
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-handshake text-info me-2"></i>
                                        مراجعة اتفاقيات الموردين
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

@media print {
    .btn-group, .card-header .btn-group {
        display: none !important;
    }
}

.progress {
    background-color: #e9ecef;
}

.table-danger {
    --bs-table-accent-bg: rgba(220, 53, 69, 0.1);
}

.table-warning {
    --bs-table-accent-bg: rgba(255, 193, 7, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['مضادات حيوية', 'مسكنات', 'أدوية السكري', 'أدوية الضغط', 'أخرى'],
            datasets: [{
                data: [8, 6, 5, 4, 9],
                backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#6f42c1']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Warehouse Chart
    const warehouseCtx = document.getElementById('warehouseChart').getContext('2d');
    new Chart(warehouseCtx, {
        type: 'bar',
        data: {
            labels: ['المستودع الرئيسي', 'مستودع الطوارئ', 'مستودع الأقسام'],
            datasets: [{
                label: 'حالة حرجة',
                data: [10, 3, 2],
                backgroundColor: '#dc3545'
            }, {
                label: 'تحذير',
                data: [12, 3, 2],
                backgroundColor: '#ffc107'
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true,
                    beginAtZero: true
                }
            }
        }
    });
}

function createUrgentOrder(drugCode) {
    if (confirm('هل تريد إنشاء أمر شراء عاجل لهذا الدواء؟')) {
        showToast('تم إنشاء أمر شراء عاجل بنجاح', 'success');
        // Implementation for creating urgent order
    }
}

function planOrder(drugCode) {
    if (confirm('هل تريد جدولة أمر شراء لهذا الدواء؟')) {
        showToast('تم جدولة أمر الشراء بنجاح', 'info');
        // Implementation for planning order
    }
}

function exportToExcel() {
    showToast('جاري تصدير التقرير إلى Excel...', 'info');
    // Implementation for Excel export
}

function exportToPDF() {
    showToast('جاري تصدير التقرير إلى PDF...', 'info');
    // Implementation for PDF export
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
