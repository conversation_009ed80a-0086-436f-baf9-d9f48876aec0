{% extends 'base/base.html' %}
{% load static %}

{% block title %}قوالب التقارير - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">قوالب التقارير</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt text-info me-2"></i>
                قوالب التقارير
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'reports:custom_report' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء قالب جديد
                </a>
                <button type="button" class="btn btn-outline-secondary" onclick="importTemplate()">
                    <i class="fas fa-upload me-2"></i>
                    استيراد قالب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control" placeholder="البحث في القوالب..." id="searchInput">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="typeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="inventory">مخزون</option>
                            <option value="orders">طلبات</option>
                            <option value="financial">مالي</option>
                            <option value="usage">استخدام</option>
                            <option value="suppliers">موردين</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="accessFilter">
                            <option value="">جميع مستويات الوصول</option>
                            <option value="public">عام</option>
                            <option value="private">خاص</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-primary w-100" onclick="applyFilters()">
                            <i class="fas fa-filter me-2"></i>
                            فلترة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Templates Grid -->
<div class="row" id="templatesGrid">
    <!-- Template Card 1 -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 template-card" data-type="inventory" data-access="public">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">ملخص المخزون الشهري</h6>
                <span class="badge bg-primary">مخزون</span>
            </div>
            <div class="card-body">
                <p class="card-text text-muted small">
                    تقرير شامل لحالة المخزون مع التحليلات والرسوم البيانية
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <small class="text-muted">مصادر البيانات</small>
                        <div class="fw-bold text-primary">3</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الحقول</small>
                        <div class="fw-bold text-success">12</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الاستخدام</small>
                        <div class="fw-bold text-info">45</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        أحمد محمد
                    </small>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate(1)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="useTemplate(1)">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate(1)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(1)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Card 2 -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 template-card" data-type="orders" data-access="public">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">تحليل الطلبات الأسبوعي</h6>
                <span class="badge bg-success">طلبات</span>
            </div>
            <div class="card-body">
                <p class="card-text text-muted small">
                    تقرير تفصيلي للطلبات مع تحليل الاتجاهات والأداء
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <small class="text-muted">مصادر البيانات</small>
                        <div class="fw-bold text-primary">2</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الحقول</small>
                        <div class="fw-bold text-success">8</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الاستخدام</small>
                        <div class="fw-bold text-info">32</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        فاطمة علي
                    </small>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate(2)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="useTemplate(2)">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate(2)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(2)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Card 3 -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 template-card" data-type="financial" data-access="private">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">التقرير المالي الربع سنوي</h6>
                <span class="badge bg-warning text-dark">مالي</span>
            </div>
            <div class="card-body">
                <p class="card-text text-muted small">
                    تحليل مالي شامل للمشتريات والمصروفات والوفورات
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <small class="text-muted">مصادر البيانات</small>
                        <div class="fw-bold text-primary">4</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الحقول</small>
                        <div class="fw-bold text-success">15</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الاستخدام</small>
                        <div class="fw-bold text-info">28</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        محمد سالم
                        <i class="fas fa-lock ms-1 text-warning" title="قالب خاص"></i>
                    </small>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate(3)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="useTemplate(3)">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate(3)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(3)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Card 4 -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 template-card" data-type="suppliers" data-access="public">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">تقييم أداء الموردين</h6>
                <span class="badge bg-secondary">موردين</span>
            </div>
            <div class="card-body">
                <p class="card-text text-muted small">
                    تقرير شامل لأداء الموردين مع مؤشرات الجودة والتسليم
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <small class="text-muted">مصادر البيانات</small>
                        <div class="fw-bold text-primary">2</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الحقول</small>
                        <div class="fw-bold text-success">10</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الاستخدام</small>
                        <div class="fw-bold text-info">18</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        سارة أحمد
                    </small>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate(4)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="useTemplate(4)">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate(4)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(4)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Card 5 -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 template-card" data-type="usage" data-access="public">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">تحليل أنماط الاستخدام</h6>
                <span class="badge bg-info">استخدام</span>
            </div>
            <div class="card-body">
                <p class="card-text text-muted small">
                    تحليل تفصيلي لأنماط استخدام الأدوية والاتجاهات
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <small class="text-muted">مصادر البيانات</small>
                        <div class="fw-bold text-primary">3</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الحقول</small>
                        <div class="fw-bold text-success">11</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">الاستخدام</small>
                        <div class="fw-bold text-info">25</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">
                        <i class="fas fa-user me-1"></i>
                        خالد يوسف
                    </small>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate(5)">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="useTemplate(5)">
                            <i class="fas fa-play"></i>
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate(5)">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate(5)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Empty State -->
    <div class="col-12" id="emptyState" style="display: none;">
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد قوالب تطابق البحث</h5>
            <p class="text-muted">جرب تغيير معايير البحث أو إنشاء قالب جديد</p>
            <a href="{% url 'reports:custom_report' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء قالب جديد
            </a>
        </div>
    </div>
</div>

<!-- Pagination -->
<div class="row">
    <div class="col-12">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">السابق</a>
                </li>
                <li class="page-item active"><a class="page-link" href="#">1</a></li>
                <li class="page-item"><a class="page-link" href="#">2</a></li>
                <li class="page-item"><a class="page-link" href="#">3</a></li>
                <li class="page-item">
                    <a class="page-link" href="#">التالي</a>
                </li>
            </ul>
        </nav>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function applyFilters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;
    const accessFilter = document.getElementById('accessFilter').value;
    
    const cards = document.querySelectorAll('.template-card');
    let visibleCount = 0;
    
    cards.forEach(card => {
        const cardType = card.dataset.type;
        const cardAccess = card.dataset.access;
        const cardTitle = card.querySelector('.card-title').textContent.toLowerCase();
        const cardText = card.querySelector('.card-text').textContent.toLowerCase();
        
        const matchesSearch = !searchTerm || cardTitle.includes(searchTerm) || cardText.includes(searchTerm);
        const matchesType = !typeFilter || cardType === typeFilter;
        const matchesAccess = !accessFilter || cardAccess === accessFilter;
        
        if (matchesSearch && matchesType && matchesAccess) {
            card.parentElement.style.display = 'block';
            visibleCount++;
        } else {
            card.parentElement.style.display = 'none';
        }
    });
    
    // Show/hide empty state
    document.getElementById('emptyState').style.display = visibleCount === 0 ? 'block' : 'none';
}

function previewTemplate(id) {
    showToast(`معاينة القالب رقم ${id}`, 'info');
    // Implementation for template preview
}

function useTemplate(id) {
    if (confirm('هل تريد استخدام هذا القالب لإنشاء تقرير جديد؟')) {
        showToast(`جاري تحميل القالب رقم ${id}...`, 'success');
        // Redirect to custom report with template loaded
        setTimeout(() => {
            window.location.href = "{% url 'reports:custom_report' %}?template=" + id;
        }, 1000);
    }
}

function editTemplate(id) {
    showToast(`تحرير القالب رقم ${id}`, 'info');
    // Implementation for template editing
}

function deleteTemplate(id) {
    if (confirm('هل أنت متأكد من حذف هذا القالب؟ لا يمكن التراجع عن هذا الإجراء.')) {
        showToast(`تم حذف القالب رقم ${id}`, 'success');
        // Implementation for template deletion
        // Remove the card from DOM
        setTimeout(() => {
            const card = document.querySelector(`[onclick="deleteTemplate(${id})"]`).closest('.col-lg-4');
            card.remove();
        }, 1000);
    }
}

function importTemplate() {
    showToast('ميزة استيراد القوالب ستكون متاحة قريباً', 'info');
}

// Real-time search
document.getElementById('searchInput').addEventListener('input', applyFilters);
document.getElementById('typeFilter').addEventListener('change', applyFilters);
document.getElementById('accessFilter').addEventListener('change', applyFilters);

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
