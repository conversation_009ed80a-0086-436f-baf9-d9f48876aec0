{% extends 'base/base.html' %}
{% load static %}

{% block title %}حركات المخزون - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item active">حركات المخزون</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-history me-2"></i>
                حركات المخزون
            </h1>
            <a href="{% url 'inventory:movement_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                حركة مخزون جديدة
            </a>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-2">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ movements|length }}</h4>
                <small class="text-muted">إجمالي الحركات</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with in_count=0 %}
                        {% for movement in movements %}
                            {% if movement.movement_type == 'in' %}
                                {% with in_count=in_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ in_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">إدخال</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with out_count=0 %}
                        {% for movement in movements %}
                            {% if movement.movement_type == 'out' %}
                                {% with out_count=out_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ out_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">إخراج</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-info">
            <div class="card-body">
                <h4 class="text-info mb-1">
                    {% with transfer_count=0 %}
                        {% for movement in movements %}
                            {% if movement.movement_type == 'transfer' %}
                                {% with transfer_count=transfer_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ transfer_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">نقل</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-secondary">
            <div class="card-body">
                <h4 class="text-secondary mb-1">
                    {% with adjustment_count=0 %}
                        {% for movement in movements %}
                            {% if movement.movement_type == 'adjustment' %}
                                {% with adjustment_count=adjustment_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ adjustment_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">تسوية</small>
            </div>
        </div>
    </div>
    <div class="col-md-2">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with damage_count=0 %}
                        {% for movement in movements %}
                            {% if movement.movement_type in 'damage,expiry' %}
                                {% with damage_count=damage_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ damage_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">تلف/انتهاء</small>
            </div>
        </div>
    </div>
</div>

<!-- Movements List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    سجل حركات المخزون ({{ movements|length }} حركة)
                </h5>
            </div>
            <div class="card-body">
                {% if movements %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>التاريخ والوقت</th>
                                <th>نوع الحركة</th>
                                <th>الدواء</th>
                                <th>المستودع</th>
                                <th>الكمية</th>
                                <th>رقم المرجع</th>
                                <th>المستخدم</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in movements %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ movement.created_at|date:"d/m/Y" }}</strong>
                                        <br>
                                        <small class="text-muted">{{ movement.created_at|time:"H:i" }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if movement.movement_type == 'in' %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-arrow-down me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% elif movement.movement_type == 'out' %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-arrow-up me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% elif movement.movement_type == 'transfer' %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-exchange-alt me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% elif movement.movement_type == 'adjustment' %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-balance-scale me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% elif movement.movement_type == 'return' %}
                                    <span class="badge bg-primary">
                                        <i class="fas fa-undo me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% elif movement.movement_type == 'damage' %}
                                    <span class="badge bg-danger">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% elif movement.movement_type == 'expiry' %}
                                    <span class="badge bg-dark">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ movement.get_movement_type_display }}
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ movement.inventory_item.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ movement.inventory_item.drug.scientific_name }}</small>
                                        <br>
                                        <code class="small">{{ movement.inventory_item.batch.batch_number }}</code>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ movement.inventory_item.warehouse.name }}</span>
                                    {% if movement.movement_type == 'transfer' %}
                                        {% if movement.from_warehouse %}
                                        <br><small class="text-muted">من: {{ movement.from_warehouse.name }}</small>
                                        {% endif %}
                                        {% if movement.to_warehouse %}
                                        <br><small class="text-muted">إلى: {{ movement.to_warehouse.name }}</small>
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="fw-bold {% if movement.movement_type in 'in,return' %}text-success{% elif movement.movement_type in 'out,damage,expiry' %}text-danger{% else %}text-info{% endif %}">
                                        {% if movement.movement_type in 'in,return' %}+{% elif movement.movement_type in 'out,damage,expiry' %}-{% endif %}{{ movement.quantity }}
                                    </span>
                                    <small class="text-muted">{{ movement.inventory_item.drug.get_unit_display }}</small>
                                </td>
                                <td>
                                    {% if movement.reference_number %}
                                    <code>{{ movement.reference_number }}</code>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ movement.created_by.get_full_name|default:movement.created_by.username }}</strong>
                                        <br>
                                        <small class="text-muted">{{ movement.created_by.username }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:movement_detail' movement.pk %}" 
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if movement.notes %}
                                        <button type="button" class="btn btn-outline-secondary" 
                                                data-bs-toggle="tooltip" data-bs-placement="top" 
                                                title="{{ movement.notes }}">
                                            <i class="fas fa-sticky-note"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد حركات مخزون</h5>
                    <p class="text-muted">لم يتم تسجيل أي حركات للمخزون بعد.</p>
                    <a href="{% url 'inventory:movement_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول حركة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
