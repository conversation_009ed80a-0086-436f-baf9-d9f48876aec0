"""
URL configuration for dms_project project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', TemplateView.as_view(template_name='dashboard.html'), name='dashboard'),
    path('accounts/', include('accounts.urls')),
    path('drugs/', include('drugs.urls')),
    path('inventory/', include('inventory.urls')),
    path('institutions/', include('institutions.urls')),
    path('orders/', include('orders.urls')),
    path('procurement/', include('procurement.urls')),
    path('returns/', include('returns.urls')),
    path('reports/', include('reports.urls')),
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
