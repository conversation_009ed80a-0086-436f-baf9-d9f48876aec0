from django.urls import path
from . import views

app_name = 'drugs'

urlpatterns = [
    # Drug URLs
    path('', views.DrugListView.as_view(), name='drug_list'),
    path('create/', views.DrugCreateView.as_view(), name='drug_create'),
    path('<int:pk>/', views.DrugDetailView.as_view(), name='drug_detail'),
    path('<int:pk>/edit/', views.DrugUpdateView.as_view(), name='drug_edit'),
    path('<int:pk>/delete/', views.DrugDeleteView.as_view(), name='drug_delete'),
    
    # Category URLs
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('categories/create/', views.CategoryCreateView.as_view(), name='category_create'),
    path('categories/<int:pk>/', views.CategoryDetailView.as_view(), name='category_detail'),
    path('categories/<int:pk>/edit/', views.CategoryUpdateView.as_view(), name='category_edit'),
    path('categories/<int:pk>/delete/', views.CategoryDeleteView.as_view(), name='category_delete'),
    
    # Manufacturer URLs
    path('manufacturers/', views.ManufacturerListView.as_view(), name='manufacturer_list'),
    path('manufacturers/create/', views.ManufacturerCreateView.as_view(), name='manufacturer_create'),
    path('manufacturers/<int:pk>/', views.ManufacturerDetailView.as_view(), name='manufacturer_detail'),
    path('manufacturers/<int:pk>/edit/', views.ManufacturerUpdateView.as_view(), name='manufacturer_edit'),
    path('manufacturers/<int:pk>/delete/', views.ManufacturerDeleteView.as_view(), name='manufacturer_delete'),
    
    # API URLs for AJAX
    path('api/search/', views.drug_search_api, name='drug_search_api'),
    path('api/<int:pk>/info/', views.drug_info_api, name='drug_info_api'),
]
