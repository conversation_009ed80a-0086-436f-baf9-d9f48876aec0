from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal

class DrugCategory(models.Model):
    """تصنيف الأدوية"""
    name = models.CharField(max_length=100, verbose_name="اسم التصنيف")
    description = models.TextField(blank=True, verbose_name="الوصف")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "تصنيف الدواء"
        verbose_name_plural = "تصنيفات الأدوية"
        ordering = ['name']

    def __str__(self):
        return self.name

class Manufacturer(models.Model):
    """الشركات المصنعة"""
    name = models.CharField(max_length=200, verbose_name="اسم الشركة")
    country = models.CharField(max_length=100, verbose_name="البلد")
    contact_info = models.TextField(blank=True, verbose_name="معلومات الاتصال")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "الشركة المصنعة"
        verbose_name_plural = "الشركات المصنعة"
        ordering = ['name']

    def __str__(self):
        return self.name

class Drug(models.Model):
    """الأدوية"""
    UNIT_CHOICES = [
        ('tablet', 'قرص'),
        ('capsule', 'كبسولة'),
        ('bottle', 'زجاجة'),
        ('vial', 'أمبولة'),
        ('tube', 'أنبوب'),
        ('box', 'علبة'),
        ('pack', 'عبوة'),
        ('ml', 'مل'),
        ('mg', 'مجم'),
        ('g', 'جرام'),
    ]

    # معلومات أساسية
    scientific_name = models.CharField(max_length=200, verbose_name="الاسم العلمي")
    commercial_name = models.CharField(max_length=200, verbose_name="الاسم التجاري")
    barcode = models.CharField(max_length=50, unique=True, blank=True, null=True, verbose_name="الباركود")

    # التصنيف والشركة
    category = models.ForeignKey(DrugCategory, on_delete=models.PROTECT, verbose_name="التصنيف")
    manufacturer = models.ForeignKey(Manufacturer, on_delete=models.PROTECT, verbose_name="الشركة المصنعة")

    # معلومات الوحدة والسعر
    unit = models.CharField(max_length=20, choices=UNIT_CHOICES, verbose_name="الوحدة")
    unit_price = models.DecimalField(max_digits=10, decimal_places=2,
                                   validators=[MinValueValidator(Decimal('0.01'))],
                                   verbose_name="سعر الوحدة")

    # معلومات إضافية
    strength = models.CharField(max_length=100, blank=True, verbose_name="التركيز")
    dosage_form = models.CharField(max_length=100, blank=True, verbose_name="الشكل الصيدلاني")
    description = models.TextField(blank=True, verbose_name="الوصف")

    # حالة الدواء
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    requires_prescription = models.BooleanField(default=True, verbose_name="يتطلب وصفة طبية")
    is_controlled = models.BooleanField(default=False, verbose_name="دواء مراقب")

    # تواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "الدواء"
        verbose_name_plural = "الأدوية"
        ordering = ['scientific_name']
        unique_together = ['scientific_name', 'manufacturer', 'strength']

    def __str__(self):
        return f"{self.commercial_name} ({self.scientific_name})"

    @property
    def full_name(self):
        """الاسم الكامل للدواء"""
        name = f"{self.commercial_name}"
        if self.strength:
            name += f" {self.strength}"
        return name
