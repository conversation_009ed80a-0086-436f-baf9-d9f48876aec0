from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from .models import DrugOrder, DrugOrderItem, InternalRequest, InternalRequestItem

class DrugOrderListView(LoginRequiredMixin, ListView):
    model = DrugOrder
    template_name = 'orders/drug_order_list.html'
    context_object_name = 'orders'
    paginate_by = 20

class DrugOrderDetailView(LoginRequiredMixin, DetailView):
    model = DrugOrder
    template_name = 'orders/drug_order_detail.html'
    context_object_name = 'order'

class DrugOrderCreateView(LoginRequiredMixin, CreateView):
    model = DrugOrder
    template_name = 'orders/drug_order_form.html'
    fields = ['institution', 'department', 'priority', 'notes', 'delivery_address', 'expected_delivery_date']
    success_url = reverse_lazy('orders:order_list')

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        return super().form_valid(form)

class DrugOrderUpdateView(LoginRequiredMixin, UpdateView):
    model = DrugOrder
    template_name = 'orders/drug_order_form.html'
    fields = ['institution', 'department', 'priority', 'notes', 'delivery_address', 'expected_delivery_date']
    success_url = reverse_lazy('orders:order_list')

class DrugOrderDeleteView(LoginRequiredMixin, DeleteView):
    model = DrugOrder
    template_name = 'orders/drug_order_confirm_delete.html'
    success_url = reverse_lazy('orders:order_list')

class InternalRequestListView(LoginRequiredMixin, ListView):
    model = InternalRequest
    template_name = 'orders/internal_request_list.html'
    context_object_name = 'requests'
    paginate_by = 20

class InternalRequestDetailView(LoginRequiredMixin, DetailView):
    model = InternalRequest
    template_name = 'orders/internal_request_detail.html'
    context_object_name = 'request'

class InternalRequestCreateView(LoginRequiredMixin, CreateView):
    model = InternalRequest
    template_name = 'orders/internal_request_form.html'
    fields = ['department', 'warehouse', 'request_type', 'justification', 'notes', 'needed_by']
    success_url = reverse_lazy('orders:internal_request_list')

    def form_valid(self, form):
        form.instance.requested_by = self.request.user
        return super().form_valid(form)

class InternalRequestUpdateView(LoginRequiredMixin, UpdateView):
    model = InternalRequest
    template_name = 'orders/internal_request_form.html'
    fields = ['department', 'warehouse', 'request_type', 'justification', 'notes', 'needed_by']
    success_url = reverse_lazy('orders:internal_request_list')

class InternalRequestDeleteView(LoginRequiredMixin, DeleteView):
    model = InternalRequest
    template_name = 'orders/internal_request_confirm_delete.html'
    success_url = reverse_lazy('orders:internal_request_list')

class PendingOrdersReportView(LoginRequiredMixin, ListView):
    model = DrugOrder
    template_name = 'orders/pending_orders_report.html'
    context_object_name = 'orders'

    def get_queryset(self):
        return DrugOrder.objects.filter(status='pending')

class FulfilledOrdersReportView(LoginRequiredMixin, ListView):
    model = DrugOrder
    template_name = 'orders/fulfilled_orders_report.html'
    context_object_name = 'orders'

    def get_queryset(self):
        return DrugOrder.objects.filter(status='fulfilled')

# Action Views
@login_required
def approve_drug_order(request, pk):
    order = get_object_or_404(DrugOrder, pk=pk)
    order.status = 'approved'
    order.approved_by = request.user
    order.save()
    messages.success(request, f'تم الموافقة على الطلب {order.order_number}')
    return redirect('orders:order_detail', pk=pk)

@login_required
def reject_drug_order(request, pk):
    order = get_object_or_404(DrugOrder, pk=pk)
    order.status = 'rejected'
    order.approved_by = request.user
    order.save()
    messages.warning(request, f'تم رفض الطلب {order.order_number}')
    return redirect('orders:order_detail', pk=pk)

@login_required
def fulfill_drug_order(request, pk):
    order = get_object_or_404(DrugOrder, pk=pk)
    order.status = 'fulfilled'
    order.save()
    messages.success(request, f'تم تنفيذ الطلب {order.order_number}')
    return redirect('orders:order_detail', pk=pk)

@login_required
def print_drug_order(request, pk):
    order = get_object_or_404(DrugOrder, pk=pk)
    # Here you would implement PDF generation
    return HttpResponse(f"طباعة الطلب {order.order_number}")

@login_required
def approve_internal_request(request, pk):
    internal_request = get_object_or_404(InternalRequest, pk=pk)
    internal_request.status = 'approved'
    internal_request.approved_by = request.user
    internal_request.save()
    messages.success(request, f'تم الموافقة على الطلب {internal_request.request_number}')
    return redirect('orders:internal_request_detail', pk=pk)

@login_required
def reject_internal_request(request, pk):
    internal_request = get_object_or_404(InternalRequest, pk=pk)
    internal_request.status = 'rejected'
    internal_request.approved_by = request.user
    internal_request.save()
    messages.warning(request, f'تم رفض الطلب {internal_request.request_number}')
    return redirect('orders:internal_request_detail', pk=pk)

@login_required
def fulfill_internal_request(request, pk):
    internal_request = get_object_or_404(InternalRequest, pk=pk)
    internal_request.status = 'fulfilled'
    internal_request.save()
    messages.success(request, f'تم تنفيذ الطلب {internal_request.request_number}')
    return redirect('orders:internal_request_detail', pk=pk)

# Item Management Views
@login_required
def add_order_item(request, order_id):
    # Implementation for adding order items
    return JsonResponse({'status': 'success'})

@login_required
def edit_order_item(request, item_id):
    # Implementation for editing order items
    return JsonResponse({'status': 'success'})

@login_required
def delete_order_item(request, item_id):
    # Implementation for deleting order items
    return JsonResponse({'status': 'success'})

# API Views
def order_status_api(request):
    """API للحصول على حالة الطلبات"""
    orders = DrugOrder.objects.values('id', 'order_number', 'status', 'created_at')
    return JsonResponse({'orders': list(orders)})
