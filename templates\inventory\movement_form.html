{% extends 'base/base.html' %}
{% load static %}

{% block title %}
{% if object %}تعديل حركة المخزون{% else %}إضافة حركة مخزون جديدة{% endif %} - نظام إدارة الأدوية
{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:movement_list' %}">حركات المخزون</a></li>
        <li class="breadcrumb-item active">
            {% if object %}تعديل حركة المخزون{% else %}إضافة حركة مخزون جديدة{% endif %}
        </li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-2"></i>
                    {% if object %}تعديل حركة المخزون{% else %}إضافة حركة مخزون جديدة{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="movementForm">
                    {% csrf_token %}
                    
                    <!-- Movement Type -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">نوع الحركة</h6>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.movement_type.id_for_label }}" class="form-label">
                                نوع الحركة <span class="text-danger">*</span>
                            </label>
                            {{ form.movement_type }}
                            {% if form.movement_type.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.movement_type.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                اختر نوع الحركة المناسب (إدخال، إخراج، نقل، تسوية، إرجاع، تلف، انتهاء صلاحية)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Drug and Inventory Item -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات الدواء</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.inventory_item.id_for_label }}" class="form-label">
                                عنصر المخزون <span class="text-danger">*</span>
                            </label>
                            {{ form.inventory_item }}
                            {% if form.inventory_item.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.inventory_item.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                اختر الدواء والمستودع والدفعة
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                الكمية <span class="text-danger">*</span>
                            </label>
                            {{ form.quantity }}
                            {% if form.quantity.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.quantity.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أدخل الكمية بالوحدة المناسبة
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transfer Information (shown only for transfer type) -->
                    <div class="row mb-4" id="transferSection" style="display: none;">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات النقل</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.from_warehouse.id_for_label }}" class="form-label">
                                من المستودع
                            </label>
                            {{ form.from_warehouse }}
                            {% if form.from_warehouse.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.from_warehouse.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.to_warehouse.id_for_label }}" class="form-label">
                                إلى المستودع
                            </label>
                            {{ form.to_warehouse }}
                            {% if form.to_warehouse.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.to_warehouse.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">معلومات إضافية</h6>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.reference_number.id_for_label }}" class="form-label">
                                رقم المرجع
                            </label>
                            {{ form.reference_number }}
                            {% if form.reference_number.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.reference_number.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                رقم الطلب أو الفاتورة أو المرجع (اختياري)
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="{{ form.unit_cost.id_for_label }}" class="form-label">
                                تكلفة الوحدة
                            </label>
                            {{ form.unit_cost }}
                            {% if form.unit_cost.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.unit_cost.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                تكلفة الوحدة الواحدة (اختياري)
                            </div>
                        </div>
                        
                        <div class="col-12 mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">
                                ملاحظات
                            </label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger small mt-1">
                                    {{ form.notes.errors.0 }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                أي ملاحظات إضافية حول هذه الحركة (اختياري)
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'inventory:movement_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% if object %}تحديث الحركة{% else %}إضافة الحركة{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Movement Types Guide -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    دليل أنواع الحركات
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-success me-2">
                                <i class="fas fa-arrow-down"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">إدخال</h6>
                                <small class="text-muted">إضافة مخزون جديد</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-warning text-dark me-2">
                                <i class="fas fa-arrow-up"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">إخراج</h6>
                                <small class="text-muted">صرف أو بيع</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-info me-2">
                                <i class="fas fa-exchange-alt"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">نقل</h6>
                                <small class="text-muted">نقل بين المستودعات</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-secondary me-2">
                                <i class="fas fa-balance-scale"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">تسوية</h6>
                                <small class="text-muted">تصحيح الكميات</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary me-2">
                                <i class="fas fa-undo"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">إرجاع</h6>
                                <small class="text-muted">إرجاع من العملاء</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-danger me-2">
                                <i class="fas fa-exclamation-triangle"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">تلف</h6>
                                <small class="text-muted">أدوية تالفة</small>
                            </div>
                        </div>
                    </div>
                    <div class="list-group-item px-0 py-2">
                        <div class="d-flex align-items-center">
                            <span class="badge bg-dark me-2">
                                <i class="fas fa-clock"></i>
                            </span>
                            <div>
                                <h6 class="mb-0 small">انتهاء صلاحية</h6>
                                <small class="text-muted">أدوية منتهية</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مفيدة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">إرشادات مهمة:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من اختيار نوع الحركة الصحيح</li>
                        <li>تحقق من الكمية المتاحة قبل الإخراج</li>
                        <li>أضف رقم مرجع للتتبع</li>
                        <li>اكتب ملاحظات واضحة</li>
                        <li>للنقل: تأكد من اختيار المستودعات الصحيحة</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- Current Stock Info (if editing) -->
        {% if object %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات الحركة
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">تاريخ الإنشاء:</span>
                        <span>{{ object.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">المستخدم:</span>
                        <span>{{ object.created_by.get_full_name|default:object.created_by.username }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">رقم الحركة:</span>
                        <span><code>#{{ object.id }}</code></span>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const movementTypeField = document.getElementById('{{ form.movement_type.id_for_label }}');
    const transferSection = document.getElementById('transferSection');
    
    // Show/hide transfer section based on movement type
    function toggleTransferSection() {
        if (movementTypeField.value === 'transfer') {
            transferSection.style.display = 'block';
        } else {
            transferSection.style.display = 'none';
        }
    }
    
    // Initial check
    toggleTransferSection();
    
    // Listen for changes
    movementTypeField.addEventListener('change', toggleTransferSection);
    
    // Form validation
    document.getElementById('movementForm').addEventListener('submit', function(e) {
        const movementType = movementTypeField.value;
        const quantity = document.getElementById('{{ form.quantity.id_for_label }}').value;
        
        if (!movementType) {
            alert('يرجى اختيار نوع الحركة');
            e.preventDefault();
            return;
        }
        
        if (!quantity || quantity <= 0) {
            alert('يرجى إدخال كمية صحيحة');
            e.preventDefault();
            return;
        }
        
        if (movementType === 'transfer') {
            const fromWarehouse = document.getElementById('{{ form.from_warehouse.id_for_label }}').value;
            const toWarehouse = document.getElementById('{{ form.to_warehouse.id_for_label }}').value;
            
            if (!fromWarehouse || !toWarehouse) {
                alert('يرجى اختيار المستودع المصدر والمستودع الهدف للنقل');
                e.preventDefault();
                return;
            }
            
            if (fromWarehouse === toWarehouse) {
                alert('لا يمكن النقل من وإلى نفس المستودع');
                e.preventDefault();
                return;
            }
        }
    });
    
    // Auto-focus on first field
    if (movementTypeField) {
        movementTypeField.focus();
    }
});
</script>
{% endblock %}
