/* Custom CSS for DMS */

/* Arabic Font */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

/* RTL Support */
.rtl {
    direction: rtl;
    text-align: right;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    padding: 1rem 1.25rem;
}

/* Buttons */
.btn {
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-success {
    background-color: #198754;
    border-color: #198754;
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Tables */
.table {
    background-color: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Forms */
.form-control, .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* Dashboard Cards */
.dashboard-card {
    transition: transform 0.2s ease-in-out;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dashboard-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.status-active {
    background-color: #198754;
    color: white;
}

.status-inactive {
    background-color: #6c757d;
    color: white;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-approved {
    background-color: #198754;
    color: white;
}

.status-rejected {
    background-color: #dc3545;
    color: white;
}

.status-low-stock {
    background-color: #fd7e14;
    color: white;
}

.status-out-of-stock {
    background-color: #dc3545;
    color: white;
}

.status-expired {
    background-color: #dc3545;
    color: white;
}

.status-expiring-soon {
    background-color: #ffc107;
    color: #000;
}

/* Pagination */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    color: #0d6efd;
    border-color: #dee2e6;
    padding: 0.75rem 1rem;
}

.page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Search and Filters */
.search-form {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .navbar-nav .nav-link {
        padding: 0.5rem 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar, .btn, .pagination, .search-form {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
}

/* Custom Utilities */
.text-primary-custom {
    color: #0d6efd !important;
}

.bg-primary-custom {
    background-color: #0d6efd !important;
}

.border-primary-custom {
    border-color: #0d6efd !important;
}

.shadow-custom {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.rounded-custom {
    border-radius: 0.5rem !important;
}
