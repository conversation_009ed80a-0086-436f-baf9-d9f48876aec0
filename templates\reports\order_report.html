{% extends 'base/base.html' %}
{% load static %}

{% block title %}تقرير الطلبات الشامل - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:reports_dashboard' %}">التقارير</a></li>
        <li class="breadcrumb-item active">تقرير الطلبات الشامل</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-medical text-success me-2"></i>
                تقرير الطلبات الشامل
            </h1>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-success" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-2"></i>
                    تصدير Excel
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf me-2"></i>
                    تصدير PDF
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Report Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>
                    فلاتر التقرير
                </h6>
            </div>
            <div class="card-body">
                <form id="reportFilters" class="row g-3">
                    <div class="col-md-3">
                        <label for="period" class="form-label">الفترة الزمنية</label>
                        <select class="form-select" id="period" name="period">
                            <option value="last_month">الشهر الماضي</option>
                            <option value="last_3_months">آخر 3 أشهر</option>
                            <option value="last_6_months">آخر 6 أشهر</option>
                            <option value="last_year">السنة الماضية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">حالة الطلب</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="pending">في الانتظار</option>
                            <option value="approved">موافق عليه</option>
                            <option value="fulfilled">مكتمل</option>
                            <option value="cancelled">ملغي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="institution" class="form-label">المؤسسة</label>
                        <select class="form-select" id="institution" name="institution">
                            <option value="">جميع المؤسسات</option>
                            <option value="1">مستشفى الملك فهد</option>
                            <option value="2">مركز الرعاية الأولية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-sync me-2"></i>
                                تحديث التقرير
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">1,245</h4>
                <small class="text-muted">إجمالي الطلبات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">980</h4>
                <small class="text-muted">طلبات مكتملة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">185</h4>
                <small class="text-muted">طلبات معلقة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">80</h4>
                <small class="text-muted">طلبات ملغية</small>
            </div>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    اتجاه الطلبات الشهري
                </h6>
            </div>
            <div class="card-body">
                <canvas id="ordersChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع حالات الطلبات
                </h6>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Orders Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل الطلبات
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>المؤسسة</th>
                                <th>تاريخ الطلب</th>
                                <th>عدد الأصناف</th>
                                <th>القيمة الإجمالية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>ORD-2024-001</strong></td>
                                <td>مستشفى الملك فهد</td>
                                <td>2024-12-15</td>
                                <td>15</td>
                                <td>45,000 ريال</td>
                                <td><span class="badge bg-success">مكتمل</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>ORD-2024-002</strong></td>
                                <td>مركز الرعاية الأولية</td>
                                <td>2024-12-14</td>
                                <td>8</td>
                                <td>22,500 ريال</td>
                                <td><span class="badge bg-warning">معلق</span></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // Orders Trend Chart
    const ordersCtx = document.getElementById('ordersChart').getContext('2d');
    new Chart(ordersCtx, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'عدد الطلبات',
                data: [120, 150, 180, 140, 200, 185],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['مكتملة', 'معلقة', 'ملغية'],
            datasets: [{
                data: [980, 185, 80],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function generateReport() {
    showToast('تم تحديث التقرير بنجاح', 'success');
}

function exportToExcel() {
    showToast('جاري تصدير التقرير إلى Excel...', 'info');
}

function exportToPDF() {
    showToast('جاري تصدير التقرير إلى PDF...', 'info');
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-info-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
