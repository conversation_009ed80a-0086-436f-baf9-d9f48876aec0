{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل حركة المخزون #{{ movement.id }} - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:movement_list' %}">حركات المخزون</a></li>
        <li class="breadcrumb-item active">حركة #{{ movement.id }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-exchange-alt me-2"></i>
                تفاصيل حركة المخزون #{{ movement.id }}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'inventory:movement_edit' movement.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <a href="{% url 'inventory:movement_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Movement Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحركة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">نوع الحركة</label>
                        <div>
                            {% if movement.movement_type == 'in' %}
                            <span class="badge bg-success fs-6">
                                <i class="fas fa-arrow-down me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% elif movement.movement_type == 'out' %}
                            <span class="badge bg-warning text-dark fs-6">
                                <i class="fas fa-arrow-up me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% elif movement.movement_type == 'transfer' %}
                            <span class="badge bg-info fs-6">
                                <i class="fas fa-exchange-alt me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% elif movement.movement_type == 'adjustment' %}
                            <span class="badge bg-secondary fs-6">
                                <i class="fas fa-balance-scale me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% elif movement.movement_type == 'return' %}
                            <span class="badge bg-primary fs-6">
                                <i class="fas fa-undo me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% elif movement.movement_type == 'damage' %}
                            <span class="badge bg-danger fs-6">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% elif movement.movement_type == 'expiry' %}
                            <span class="badge bg-dark fs-6">
                                <i class="fas fa-clock me-1"></i>
                                {{ movement.get_movement_type_display }}
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الكمية</label>
                        <p class="fw-bold {% if movement.movement_type in 'in,return' %}text-success{% elif movement.movement_type in 'out,damage,expiry' %}text-danger{% else %}text-info{% endif %}">
                            {% if movement.movement_type in 'in,return' %}+{% elif movement.movement_type in 'out,damage,expiry' %}-{% endif %}{{ movement.quantity }}
                            <small class="text-muted">{{ movement.inventory_item.drug.get_unit_display }}</small>
                        </p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ ووقت الحركة</label>
                        <p class="fw-bold">{{ movement.created_at|date:"d/m/Y H:i" }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المستخدم</label>
                        <p class="fw-bold">{{ movement.created_by.get_full_name|default:movement.created_by.username }}</p>
                    </div>
                    
                    {% if movement.reference_number %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم المرجع</label>
                        <p><code>{{ movement.reference_number }}</code></p>
                    </div>
                    {% endif %}
                    
                    {% if movement.unit_cost %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تكلفة الوحدة</label>
                        <p class="text-success fw-bold">{{ movement.unit_cost }} ريال</p>
                    </div>
                    {% endif %}
                    
                    {% if movement.notes %}
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">الملاحظات</label>
                        <p>{{ movement.notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Drug Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pills me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم التجاري</label>
                        <p class="fw-bold">{{ movement.inventory_item.drug.commercial_name }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم العلمي</label>
                        <p class="fw-bold">{{ movement.inventory_item.drug.scientific_name }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">التصنيف</label>
                        <p><span class="badge bg-primary">{{ movement.inventory_item.drug.category.name }}</span></p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الشركة المصنعة</label>
                        <p>{{ movement.inventory_item.drug.manufacturer.name }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المستودع</label>
                        <p><span class="badge bg-info">{{ movement.inventory_item.warehouse.name }}</span></p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم الدفعة</label>
                        <p><code>{{ movement.inventory_item.batch.batch_number }}</code></p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Transfer Information (if applicable) -->
        {% if movement.movement_type == 'transfer' %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-truck me-2"></i>
                    معلومات النقل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if movement.from_warehouse %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">من المستودع</label>
                        <p><span class="badge bg-warning text-dark">{{ movement.from_warehouse.name }}</span></p>
                    </div>
                    {% endif %}
                    
                    {% if movement.to_warehouse %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">إلى المستودع</label>
                        <p><span class="badge bg-success">{{ movement.to_warehouse.name }}</span></p>
                    </div>
                    {% endif %}
                </div>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    تم نقل {{ movement.quantity }} {{ movement.inventory_item.drug.get_unit_display }} 
                    من {{ movement.from_warehouse.name|default:"غير محدد" }} 
                    إلى {{ movement.to_warehouse.name|default:"غير محدد" }}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Financial Information -->
        {% if movement.unit_cost %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-dollar-sign me-2"></i>
                    المعلومات المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <h5 class="text-success mb-1">{{ movement.unit_cost }} ريال</h5>
                        <small class="text-muted">تكلفة الوحدة</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <h5 class="text-primary mb-1">{{ movement.quantity }}</h5>
                        <small class="text-muted">الكمية</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <h5 class="text-info mb-1">
                            {% widthratio movement.unit_cost 1 movement.quantity %} ريال
                        </h5>
                        <small class="text-muted">إجمالي التكلفة</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'inventory:movement_edit' movement.pk %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الحركة
                    </a>
                    <a href="{% url 'inventory:inventory_item_detail' movement.inventory_item.pk %}" class="btn btn-outline-info">
                        <i class="fas fa-warehouse me-2"></i>
                        عرض عنصر المخزون
                    </a>
                    <a href="{% url 'drugs:drug_detail' movement.inventory_item.drug.pk %}" class="btn btn-outline-primary">
                        <i class="fas fa-pills me-2"></i>
                        عرض تفاصيل الدواء
                    </a>
                    <a href="{% url 'inventory:movement_create' %}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        حركة جديدة
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Movement Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    ملخص الحركة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h4 class="{% if movement.movement_type in 'in,return' %}text-success{% elif movement.movement_type in 'out,damage,expiry' %}text-danger{% else %}text-info{% endif %} mb-1">
                            {% if movement.movement_type in 'in,return' %}+{% elif movement.movement_type in 'out,damage,expiry' %}-{% endif %}{{ movement.quantity }}
                        </h4>
                        <small class="text-muted">{{ movement.inventory_item.drug.get_unit_display }}</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">رقم الحركة:</span>
                        <span><code>#{{ movement.id }}</code></span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="text-muted">التاريخ:</span>
                        <span>{{ movement.created_at|date:"d/m/Y" }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span class="text-muted">الوقت:</span>
                        <span>{{ movement.created_at|time:"H:i" }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Related Movements -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    حركات مرتبطة
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center py-3">
                    <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                    <h6 class="text-muted">قريباً</h6>
                    <p class="text-muted small">سيتم عرض الحركات المرتبطة هنا</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
