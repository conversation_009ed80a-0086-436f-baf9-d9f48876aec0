{% extends 'base/base.html' %}
{% load static %}

{% block title %}حذف التصنيف: {{ category.name }} - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:category_list' %}">تصنيفات الأدوية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'drugs:category_detail' category.pk %}">{{ category.name }}</a></li>
        <li class="breadcrumb-item active">حذف التصنيف</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد حذف التصنيف
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h6 class="alert-heading">
                        <i class="fas fa-warning me-2"></i>
                        تحذير مهم!
                    </h6>
                    <p class="mb-0">
                        أنت على وشك حذف التصنيف نهائياً. هذا الإجراء لا يمكن التراجع عنه وقد يؤثر على:
                    </p>
                    <ul class="mt-2 mb-0">
                        <li>الأدوية المرتبطة بهذا التصنيف</li>
                        <li>التقارير والإحصائيات</li>
                        <li>عمليات البحث والفلترة</li>
                    </ul>
                </div>

                <!-- Category Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">معلومات التصنيف المراد حذفه</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">اسم التصنيف</label>
                                <p class="fw-bold">{{ category.name }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">عدد الأدوية المرتبطة</label>
                                <p class="fw-bold text-warning">{{ category.drug_set.count }} دواء</p>
                            </div>
                            {% if category.description %}
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted small">الوصف</label>
                                <p>{{ category.description }}</p>
                            </div>
                            {% endif %}
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">تاريخ الإنشاء</label>
                                <p>{{ category.created_at|date:"d/m/Y H:i" }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted small">رقم التصنيف</label>
                                <p><code>#{{ category.id }}</code></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Impact Assessment -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">تقييم التأثير</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-4 mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-warning mb-1">{{ category.drug_set.count }}</h5>
                                    <small class="text-muted">دواء مرتبط</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-success mb-1">{{ category.drug_set.filter.is_active.count|default:0 }}</h5>
                                    <small class="text-muted">دواء نشط</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="border rounded p-3">
                                    <h5 class="text-danger mb-1">{{ category.drug_set.filter.is_controlled.count|default:0 }}</h5>
                                    <small class="text-muted">دواء مراقب</small>
                                </div>
                            </div>
                        </div>
                        
                        {% if category.drug_set.exists %}
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> يوجد {{ category.drug_set.count }} دواء مرتبط بهذا التصنيف. 
                            حذف التصنيف سيؤدي إلى فقدان تصنيف هذه الأدوية.
                        </div>
                        
                        <!-- Show some related drugs -->
                        {% if category.drug_set.count > 0 %}
                        <div class="mt-3">
                            <h6 class="text-muted">بعض الأدوية المرتبطة:</h6>
                            <div class="list-group">
                                {% for drug in category.drug_set.all|slice:":5" %}
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">{{ drug.commercial_name }}</h6>
                                            <small class="text-muted">{{ drug.scientific_name }}</small>
                                        </div>
                                        <span class="badge bg-primary">{{ drug.manufacturer.name }}</span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% if category.drug_set.count > 5 %}
                            <small class="text-muted">وهناك {{ category.drug_set.count|add:"-5" }} دواء آخر...</small>
                            {% endif %}
                        </div>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>

                <!-- Alternative Actions -->
                {% if category.drug_set.exists %}
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">بدائل مقترحة</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">بدلاً من الحذف، يمكنك:</h6>
                            <ul class="mb-0">
                                <li>نقل الأدوية إلى تصنيف آخر أولاً</li>
                                <li>تعديل اسم التصنيف بدلاً من حذفه</li>
                                <li>إنشاء تصنيف فرعي جديد</li>
                                <li>دمج هذا التصنيف مع تصنيف مشابه</li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{% url 'drugs:category_edit' category.pk %}" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>
                                تعديل التصنيف بدلاً من الحذف
                            </a>
                            <a href="{% url 'drugs:drug_list' %}?category={{ category.id }}" class="btn btn-info">
                                <i class="fas fa-pills me-2"></i>
                                إدارة الأدوية المرتبطة
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Confirmation Form -->
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                        <label class="form-check-label" for="confirmDelete">
                            <strong>أؤكد أنني أفهم عواقب هذا الإجراء وأريد المتابعة</strong>
                        </label>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'drugs:category_detail' category.pk %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                            <i class="fas fa-trash me-2"></i>
                            حذف التصنيف نهائياً
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
        
        if (this.checked) {
            deleteButton.classList.remove('btn-secondary');
            deleteButton.classList.add('btn-danger');
        } else {
            deleteButton.classList.remove('btn-danger');
            deleteButton.classList.add('btn-secondary');
        }
    });
    
    // Additional confirmation on form submit
    document.querySelector('form').addEventListener('submit', function(e) {
        if (!confirm('هل أنت متأكد تماماً من حذف هذا التصنيف؟ لا يمكن التراجع عن هذا الإجراء.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
