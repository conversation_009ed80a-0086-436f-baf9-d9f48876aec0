from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from drugs.models import DrugCategory, Manufacturer, Drug
from inventory.models import Warehouse, Batch, InventoryItem, InventoryMovement
from orders.models import Order, OrderItem, InternalRequest, InternalRequestItem
from procurement.models import Supplier, PurchaseOrder, PurchaseOrderItem


class Command(BaseCommand):
    help = 'Generate specific data for comprehensive reports'

    def handle(self, *args, **options):
        self.stdout.write('Generating reports-specific data...')
        
        self.create_monthly_trends()
        self.create_cost_analysis_data()
        self.create_usage_patterns()
        self.create_supplier_comparison_data()
        self.create_department_usage_data()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully generated reports data!')
        )

    def create_monthly_trends(self):
        """Create data for monthly trend analysis"""
        self.stdout.write('Creating monthly trends data...')
        
        drugs = Drug.objects.all()
        warehouses = Warehouse.objects.all()
        users = User.objects.all()
        
        # Create movements for the last 12 months
        for month_offset in range(12):
            base_date = timezone.now() - timedelta(days=30 * month_offset)
            
            # Create varying amounts of movements per month
            movements_count = random.randint(50, 150)
            
            for _ in range(movements_count):
                drug = random.choice(drugs)
                warehouse = random.choice(warehouses)
                
                # Get or create inventory item
                inventory_item, created = InventoryItem.objects.get_or_create(
                    drug=drug,
                    warehouse=warehouse,
                    defaults={
                        'quantity': random.randint(100, 500),
                        'min_quantity': random.randint(10, 50),
                        'max_quantity': random.randint(200, 1000),
                        'unit_cost': Decimal(str(random.uniform(1.0, 100.0))),
                        'location': f'رف {random.randint(1, 20)}-{random.randint(1, 10)}',
                    }
                )
                
                # Create movement
                movement_type = random.choice(['in', 'out', 'transfer', 'adjustment'])
                
                if movement_type == 'in':
                    quantity = random.randint(20, 200)
                    reason = 'استلام شحنة شهرية'
                elif movement_type == 'out':
                    quantity = -random.randint(10, 100)
                    reason = 'صرف للمرضى'
                elif movement_type == 'transfer':
                    quantity = random.randint(-50, 50)
                    reason = 'نقل بين المستودعات'
                else:  # adjustment
                    quantity = random.randint(-20, 20)
                    reason = 'تسوية جرد شهرية'
                
                # Random date within the month
                movement_date = base_date - timedelta(
                    days=random.randint(0, 29),
                    hours=random.randint(0, 23),
                    minutes=random.randint(0, 59)
                )
                
                InventoryMovement.objects.create(
                    inventory_item=inventory_item,
                    movement_type=movement_type,
                    quantity=quantity,
                    reason=reason,
                    created_by=random.choice(users),
                    created_at=movement_date,
                    notes=f'حركة شهرية - {movement_date.strftime("%Y-%m")}',
                )

    def create_cost_analysis_data(self):
        """Create data for cost analysis reports"""
        self.stdout.write('Creating cost analysis data...')
        
        suppliers = Supplier.objects.all()
        drugs = Drug.objects.all()
        users = User.objects.all()
        
        # Create purchase orders with varying costs for analysis
        for supplier in suppliers:
            # Create 8-15 purchase orders per supplier
            for i in range(random.randint(8, 15)):
                po_number = f"COST-{supplier.name[:3].upper()}-{random.randint(1000, 9999)}"
                
                # Random date within last 8 months
                order_date = timezone.now() - timedelta(
                    days=random.randint(0, 240)
                )
                
                po = PurchaseOrder.objects.create(
                    po_number=po_number,
                    supplier=supplier,
                    created_by=random.choice(users),
                    status=random.choice(['received', 'confirmed', 'sent']),
                    expected_delivery_date=(order_date + timedelta(days=random.randint(7, 21))).date(),
                    notes=f'أمر شراء لتحليل التكاليف - {supplier.name}',
                    created_at=order_date,
                )
                
                # Add items with varying costs
                order_drugs = random.sample(list(drugs), random.randint(3, 8))
                for drug in order_drugs:
                    # Create cost variations (some suppliers are more expensive)
                    base_cost = random.uniform(5.0, 50.0)
                    if supplier.supplier_type == 'international':
                        cost_multiplier = random.uniform(1.2, 1.8)  # International more expensive
                    elif supplier.supplier_type == 'local':
                        cost_multiplier = random.uniform(0.8, 1.2)  # Local competitive
                    else:  # regional
                        cost_multiplier = random.uniform(1.0, 1.4)  # Regional moderate
                    
                    unit_cost = Decimal(str(base_cost * cost_multiplier))
                    quantity = random.randint(50, 300)
                    
                    PurchaseOrderItem.objects.create(
                        purchase_order=po,
                        drug=drug,
                        quantity=quantity,
                        unit_cost=unit_cost,
                        total_cost=unit_cost * quantity,
                        notes=f'تحليل تكلفة {drug.name} من {supplier.name}',
                    )

    def create_usage_patterns(self):
        """Create realistic usage patterns for different drug categories"""
        self.stdout.write('Creating usage patterns data...')
        
        categories = DrugCategory.objects.all()
        institutions = [
            'مستشفى الملك فهد',
            'مركز الرعاية الأولية',
            'مستشفى الأطفال',
            'مستشفى النساء والولادة',
            'مركز الأورام',
        ]
        
        for category in categories:
            drugs = Drug.objects.filter(category=category)
            
            # Create usage patterns based on category
            if 'مضادات حيوية' in category.name:
                # High usage in winter, moderate in summer
                usage_multiplier = [1.5, 1.3, 1.0, 0.8, 0.7, 0.6, 0.6, 0.7, 0.9, 1.1, 1.3, 1.4]
            elif 'الجهاز التنفسي' in category.name:
                # Very high usage in winter
                usage_multiplier = [2.0, 1.8, 1.2, 0.8, 0.5, 0.4, 0.3, 0.4, 0.6, 1.0, 1.5, 1.8]
            elif 'السكري' in category.name:
                # Consistent usage year-round
                usage_multiplier = [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
            else:
                # General pattern
                usage_multiplier = [1.2, 1.1, 1.0, 0.9, 0.8, 0.8, 0.9, 1.0, 1.1, 1.2, 1.3, 1.2]
            
            for month_offset in range(12):
                month_multiplier = usage_multiplier[month_offset]
                base_orders = int(10 * month_multiplier)
                
                for _ in range(random.randint(base_orders - 3, base_orders + 5)):
                    order_date = timezone.now() - timedelta(days=30 * month_offset + random.randint(0, 29))
                    order_number = f"USG-{category.name[:3].upper()}-{random.randint(1000, 9999)}"
                    
                    order = Order.objects.create(
                        order_number=order_number,
                        institution=random.choice(institutions),
                        department=random.choice(['الطوارئ', 'الأقسام الداخلية', 'العيادات الخارجية']),
                        requested_by=User.objects.order_by('?').first(),
                        status=random.choice(['fulfilled', 'approved']),
                        priority=random.choice(['normal', 'high']),
                        notes=f'طلب نمط استخدام - {category.name}',
                        created_at=order_date,
                    )
                    
                    # Add items from this category
                    category_drugs = random.sample(list(drugs), min(random.randint(1, 3), len(drugs)))
                    for drug in category_drugs:
                        quantity = int(random.randint(5, 30) * month_multiplier)
                        
                        OrderItem.objects.create(
                            order=order,
                            drug=drug,
                            quantity_requested=quantity,
                            quantity_approved=quantity,
                            unit_cost=Decimal(str(random.uniform(1.0, 50.0))),
                            notes=f'نمط استخدام {drug.name}',
                        )

    def create_supplier_comparison_data(self):
        """Create data for supplier performance comparison"""
        self.stdout.write('Creating supplier comparison data...')
        
        suppliers = Supplier.objects.all()
        
        for supplier in suppliers:
            # Create performance metrics
            delivery_times = []
            order_accuracies = []
            
            # Create 15-25 orders for statistical significance
            for i in range(random.randint(15, 25)):
                po_number = f"COMP-{supplier.name[:3].upper()}-{random.randint(1000, 9999)}"
                
                order_date = timezone.now() - timedelta(days=random.randint(30, 300))
                expected_delivery = order_date + timedelta(days=random.randint(7, 21))
                
                # Simulate supplier performance characteristics
                if supplier.rating >= 4.5:
                    # High-rated suppliers are more reliable
                    delivery_variance = random.randint(-2, 3)
                    accuracy_rate = random.uniform(0.95, 1.0)
                elif supplier.rating >= 4.0:
                    # Good suppliers
                    delivery_variance = random.randint(-3, 5)
                    accuracy_rate = random.uniform(0.90, 0.98)
                else:
                    # Lower-rated suppliers
                    delivery_variance = random.randint(-5, 10)
                    accuracy_rate = random.uniform(0.80, 0.95)
                
                actual_delivery = expected_delivery + timedelta(days=delivery_variance)
                delivery_times.append(delivery_variance)
                order_accuracies.append(accuracy_rate)
                
                po = PurchaseOrder.objects.create(
                    po_number=po_number,
                    supplier=supplier,
                    created_by=User.objects.order_by('?').first(),
                    status='received',
                    expected_delivery_date=expected_delivery.date(),
                    actual_delivery_date=actual_delivery.date(),
                    notes=f'مقارنة أداء الموردين - {supplier.name}',
                    created_at=order_date,
                )
                
                # Add items
                drugs = random.sample(list(Drug.objects.all()), random.randint(2, 6))
                for drug in drugs:
                    unit_cost = Decimal(str(random.uniform(1.0, 80.0)))
                    quantity_ordered = random.randint(50, 200)
                    quantity_received = int(quantity_ordered * accuracy_rate)
                    
                    PurchaseOrderItem.objects.create(
                        purchase_order=po,
                        drug=drug,
                        quantity=quantity_ordered,
                        quantity_received=quantity_received,
                        unit_cost=unit_cost,
                        total_cost=unit_cost * quantity_ordered,
                        notes=f'مقارنة دقة التسليم - {drug.name}',
                    )

    def create_department_usage_data(self):
        """Create department-specific usage data"""
        self.stdout.write('Creating department usage data...')
        
        departments = {
            'الطوارئ': ['مسكنات', 'مضادات حيوية', 'أدوية القلب'],
            'الأقسام الداخلية': ['أدوية السكري', 'أدوية الضغط', 'مضادات الالتهاب'],
            'العيادات الخارجية': ['فيتامينات ومكملات', 'أدوية الجهاز التنفسي'],
            'العناية المركزة': ['أدوية القلب', 'مضادات حيوية'],
            'الأطفال': ['فيتامينات ومكملات', 'أدوية الجهاز التنفسي'],
        }
        
        institutions = [
            'مستشفى الملك فهد',
            'مركز الرعاية الأولية',
            'مستشفى الأطفال',
        ]
        
        for department, preferred_categories in departments.items():
            for _ in range(random.randint(20, 40)):
                order_date = timezone.now() - timedelta(days=random.randint(0, 180))
                order_number = f"DEPT-{department[:3].upper()}-{random.randint(1000, 9999)}"
                
                order = Order.objects.create(
                    order_number=order_number,
                    institution=random.choice(institutions),
                    department=department,
                    requested_by=User.objects.order_by('?').first(),
                    status=random.choice(['fulfilled', 'approved']),
                    priority=random.choice(['normal', 'high', 'urgent']),
                    notes=f'طلب قسم {department}',
                    created_at=order_date,
                )
                
                # Add items preferred by this department
                for category_name in preferred_categories:
                    try:
                        category = DrugCategory.objects.get(name=category_name)
                        category_drugs = Drug.objects.filter(category=category)
                        
                        if category_drugs.exists():
                            selected_drugs = random.sample(
                                list(category_drugs), 
                                min(random.randint(1, 3), len(category_drugs))
                            )
                            
                            for drug in selected_drugs:
                                OrderItem.objects.create(
                                    order=order,
                                    drug=drug,
                                    quantity_requested=random.randint(5, 50),
                                    quantity_approved=random.randint(5, 50),
                                    unit_cost=Decimal(str(random.uniform(1.0, 100.0))),
                                    notes=f'استخدام قسم {department} - {drug.name}',
                                )
                    except DrugCategory.DoesNotExist:
                        continue
