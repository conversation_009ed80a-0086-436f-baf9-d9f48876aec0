{% extends 'base/base.html' %}
{% load static %}

{% block title %}تفاصيل الطلب {{ order.order_number }} - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'orders:order_list' %}">طلبات صرف الأدوية</a></li>
        <li class="breadcrumb-item active">{{ order.order_number }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-medical me-2"></i>
                طلب صرف الأدوية {{ order.order_number }}
                {% if order.priority == 'urgent' %}
                <span class="badge bg-danger ms-2">طارئ</span>
                {% elif order.priority == 'high' %}
                <span class="badge bg-warning text-dark ms-2">عالي</span>
                {% endif %}
            </h1>
            <div class="btn-group" role="group">
                {% if order.status == 'pending' %}
                <a href="{% url 'orders:order_edit' order.pk %}" class="btn btn-warning">
                    <i class="fas fa-edit me-2"></i>
                    تعديل
                </a>
                <a href="{% url 'orders:approve_order' order.pk %}" class="btn btn-success"
                   onclick="return confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')">
                    <i class="fas fa-check me-2"></i>
                    موافقة
                </a>
                <a href="{% url 'orders:reject_order' order.pk %}" class="btn btn-danger"
                   onclick="return confirm('هل أنت متأكد من رفض هذا الطلب؟')">
                    <i class="fas fa-times me-2"></i>
                    رفض
                </a>
                {% elif order.status == 'approved' %}
                <a href="{% url 'orders:fulfill_order' order.pk %}" class="btn btn-success"
                   onclick="return confirm('هل أنت متأكد من تنفيذ هذا الطلب؟')">
                    <i class="fas fa-check-double me-2"></i>
                    تنفيذ
                </a>
                {% endif %}
                <a href="{% url 'orders:print_order' order.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Order Information -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px; font-size: 2rem;">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <h4 class="text-primary">{{ order.order_number }}</h4>
                    {% if order.status == 'pending' %}
                    <span class="badge bg-warning text-dark fs-6">في الانتظار</span>
                    {% elif order.status == 'approved' %}
                    <span class="badge bg-info fs-6">موافق عليه</span>
                    {% elif order.status == 'partially_fulfilled' %}
                    <span class="badge bg-primary fs-6">مكتمل جزئياً</span>
                    {% elif order.status == 'fulfilled' %}
                    <span class="badge bg-success fs-6">مكتمل</span>
                    {% elif order.status == 'rejected' %}
                    <span class="badge bg-danger fs-6">مرفوض</span>
                    {% elif order.status == 'cancelled' %}
                    <span class="badge bg-secondary fs-6">ملغي</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted small">المؤسسة</label>
                    <p class="fw-bold">{{ order.institution.name }}</p>
                    <small class="text-muted">{{ order.institution.type }}</small>
                </div>
                
                {% if order.department %}
                <div class="mb-3">
                    <label class="form-label text-muted small">القسم</label>
                    <p><span class="badge bg-info">{{ order.department.name }}</span></p>
                </div>
                {% endif %}
                
                <div class="mb-3">
                    <label class="form-label text-muted small">الأولوية</label>
                    <p>
                        {% if order.priority == 'urgent' %}
                        <span class="badge bg-danger">طارئ</span>
                        {% elif order.priority == 'high' %}
                        <span class="badge bg-warning text-dark">عالي</span>
                        {% elif order.priority == 'normal' %}
                        <span class="badge bg-secondary">عادي</span>
                        {% else %}
                        <span class="badge bg-light text-dark">منخفض</span>
                        {% endif %}
                    </p>
                </div>
                
                <div class="mb-3">
                    <label class="form-label text-muted small">عنوان التسليم</label>
                    <p>{{ order.delivery_address }}</p>
                </div>
                
                {% if order.expected_delivery_date %}
                <div class="mb-3">
                    <label class="form-label text-muted small">تاريخ التسليم المتوقع</label>
                    <p class="fw-bold text-info">{{ order.expected_delivery_date|date:"d/m/Y" }}</p>
                </div>
                {% endif %}
                
                {% if order.notes %}
                <div class="mb-3">
                    <label class="form-label text-muted small">الملاحظات</label>
                    <p>{{ order.notes }}</p>
                </div>
                {% endif %}
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary mb-1">{{ order.total_items }}</h4>
                        <small class="text-muted">صنف</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ order.total_amount|floatformat:2 }}</h4>
                        <small class="text-muted">ريال</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="small text-muted">
                    <div class="d-flex justify-content-between mb-2">
                        <span>تاريخ الطلب:</span>
                        <span>{{ order.created_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>طلب بواسطة:</span>
                        <span>{{ order.requested_by.get_full_name|default:order.requested_by.username }}</span>
                    </div>
                    {% if order.approved_by %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>وافق عليه:</span>
                        <span>{{ order.approved_by.get_full_name|default:order.approved_by.username }}</span>
                    </div>
                    {% endif %}
                    {% if order.approved_at %}
                    <div class="d-flex justify-content-between">
                        <span>تاريخ الموافقة:</span>
                        <span>{{ order.approved_at|date:"d/m/Y H:i" }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if order.status == 'pending' %}
                    <a href="{% url 'orders:order_edit' order.pk %}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الطلب
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="addOrderItem()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة دواء
                    </button>
                    {% endif %}
                    <a href="{% url 'orders:print_order' order.pk %}" class="btn btn-outline-secondary">
                        <i class="fas fa-print me-2"></i>
                        طباعة الطلب
                    </a>
                    <a href="{% url 'orders:order_create' %}" class="btn btn-outline-success">
                        <i class="fas fa-copy me-2"></i>
                        نسخ الطلب
                    </a>
                    <a href="{% url 'orders:order_list' %}" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>
                        جميع الطلبات
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Order Items -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pills me-2"></i>
                    الأدوية المطلوبة ({{ order.items.count }})
                </h5>
                {% if order.status == 'pending' %}
                <button type="button" class="btn btn-sm btn-primary" onclick="addOrderItem()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة دواء
                </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if order.items.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>الكمية المطلوبة</th>
                                <th>الكمية الموافق عليها</th>
                                <th>الكمية المسلمة</th>
                                <th>سعر الوحدة</th>
                                <th>إجمالي السعر</th>
                                <th>الحالة</th>
                                {% if order.status == 'pending' %}
                                <th>الإجراءات</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order.items.all %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ item.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ item.drug.scientific_name }}</small>
                                        <br>
                                        <span class="badge bg-primary">{{ item.drug.category.name }}</span>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-primary">{{ item.requested_quantity }}</span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                </td>
                                <td>
                                    <span class="fw-bold text-info">{{ item.approved_quantity }}</span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                </td>
                                <td>
                                    <span class="fw-bold text-success">{{ item.delivered_quantity }}</span>
                                    <small class="text-muted">{{ item.drug.get_unit_display }}</small>
                                </td>
                                <td class="text-success">{{ item.unit_price }} ريال</td>
                                <td class="text-success fw-bold">{{ item.total_price|floatformat:2 }} ريال</td>
                                <td>
                                    {% if item.is_fully_delivered %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif item.delivered_quantity > 0 %}
                                    <span class="badge bg-primary">جزئي</span>
                                    {% elif item.approved_quantity > 0 %}
                                    <span class="badge bg-info">موافق عليه</span>
                                    {% else %}
                                    <span class="badge bg-warning text-dark">في الانتظار</span>
                                    {% endif %}
                                </td>
                                {% if order.status == 'pending' %}
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="editOrderItem({{ item.pk }})" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteOrderItem({{ item.pk }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                                {% endif %}
                            </tr>
                            {% if item.notes %}
                            <tr>
                                <td colspan="{% if order.status == 'pending' %}8{% else %}7{% endif %}" class="border-0 pt-0">
                                    <small class="text-muted">
                                        <i class="fas fa-sticky-note me-1"></i>
                                        {{ item.notes }}
                                    </small>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <th colspan="5">الإجمالي:</th>
                                <th class="text-success">{{ order.total_amount|floatformat:2 }} ريال</th>
                                <th colspan="{% if order.status == 'pending' %}2{% else %}1{% endif %}"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-pills fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أدوية مطلوبة</h5>
                    <p class="text-muted">لم يتم إضافة أي أدوية لهذا الطلب بعد.</p>
                    {% if order.status == 'pending' %}
                    <button type="button" class="btn btn-primary" onclick="addOrderItem()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دواء
                    </button>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Order Timeline -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    تاريخ الطلب
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">إنشاء الطلب</h6>
                            <p class="timeline-text">
                                تم إنشاء الطلب بواسطة {{ order.requested_by.get_full_name|default:order.requested_by.username }}
                            </p>
                            <small class="text-muted">{{ order.created_at|date:"d/m/Y H:i" }}</small>
                        </div>
                    </div>
                    
                    {% if order.approved_at %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">الموافقة على الطلب</h6>
                            <p class="timeline-text">
                                تم الموافقة على الطلب بواسطة {{ order.approved_by.get_full_name|default:order.approved_by.username }}
                            </p>
                            <small class="text-muted">{{ order.approved_at|date:"d/m/Y H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if order.status == 'fulfilled' %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">تنفيذ الطلب</h6>
                            <p class="timeline-text">تم تنفيذ الطلب بالكامل</p>
                            <small class="text-muted">{{ order.updated_at|date:"d/m/Y H:i" }}</small>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function addOrderItem() {
    alert('سيتم تطوير هذه الميزة قريباً - إضافة دواء للطلب');
}

function editOrderItem(itemId) {
    alert('سيتم تطوير هذه الميزة قريباً - تعديل عنصر الطلب');
}

function deleteOrderItem(itemId) {
    if (confirm('هل أنت متأكد من حذف هذا الدواء من الطلب؟')) {
        alert('سيتم تطوير هذه الميزة قريباً - حذف عنصر الطلب');
    }
}
</script>
{% endblock %}
