from django.urls import path
from . import views

app_name = 'orders'

urlpatterns = [
    # Drug Order URLs
    path('', views.DrugOrderListView.as_view(), name='order_list'),
    path('create/', views.DrugOrderCreateView.as_view(), name='order_create'),
    path('<int:pk>/', views.DrugOrderDetailView.as_view(), name='order_detail'),
    path('<int:pk>/edit/', views.DrugOrderUpdateView.as_view(), name='order_edit'),
    path('<int:pk>/delete/', views.DrugOrderDeleteView.as_view(), name='order_delete'),
    path('<int:pk>/approve/', views.approve_drug_order, name='order_approve'),
    path('<int:pk>/reject/', views.reject_drug_order, name='order_reject'),
    path('<int:pk>/fulfill/', views.fulfill_drug_order, name='order_fulfill'),
    path('<int:pk>/print/', views.print_drug_order, name='order_print'),
    
    # Internal Request URLs
    path('internal/', views.InternalRequestListView.as_view(), name='internal_request_list'),
    path('internal/create/', views.InternalRequestCreateView.as_view(), name='internal_request_create'),
    path('internal/<int:pk>/', views.InternalRequestDetailView.as_view(), name='internal_request_detail'),
    path('internal/<int:pk>/edit/', views.InternalRequestUpdateView.as_view(), name='internal_request_edit'),
    path('internal/<int:pk>/delete/', views.InternalRequestDeleteView.as_view(), name='internal_request_delete'),
    path('internal/<int:pk>/approve/', views.approve_internal_request, name='internal_request_approve'),
    path('internal/<int:pk>/reject/', views.reject_internal_request, name='internal_request_reject'),
    path('internal/<int:pk>/fulfill/', views.fulfill_internal_request, name='internal_request_fulfill'),
    
    # Order Item Management
    path('<int:order_id>/items/add/', views.add_order_item, name='add_order_item'),
    path('items/<int:item_id>/edit/', views.edit_order_item, name='edit_order_item'),
    path('items/<int:item_id>/delete/', views.delete_order_item, name='delete_order_item'),
    
    # Reports
    path('reports/pending/', views.PendingOrdersReportView.as_view(), name='pending_orders_report'),
    path('reports/fulfilled/', views.FulfilledOrdersReportView.as_view(), name='fulfilled_orders_report'),
    
    # API URLs
    path('api/order-status/', views.order_status_api, name='order_status_api'),
]
