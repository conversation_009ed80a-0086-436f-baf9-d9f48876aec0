{% extends 'base/base.html' %}
{% load static %}

{% block title %}الملف الشخصي - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active">الملف الشخصي</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-user me-2"></i>
            الملف الشخصي
        </h1>
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    {% if profile and profile.avatar %}
                    <img src="{{ profile.avatar.url }}" alt="الصورة الشخصية" 
                         class="rounded-circle" width="120" height="120">
                    {% else %}
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                         style="width: 120px; height: 120px; font-size: 3rem;">
                        <i class="fas fa-user"></i>
                    </div>
                    {% endif %}
                </div>
                
                <h5 class="card-title">{{ user.get_full_name|default:user.username }}</h5>
                
                {% if profile %}
                <p class="text-muted">{{ profile.get_role_display }}</p>
                
                {% if profile.employee_id %}
                <p class="small text-muted">
                    <i class="fas fa-id-badge me-1"></i>
                    رقم الموظف: {{ profile.employee_id }}
                </p>
                {% endif %}
                
                {% if profile.department %}
                <p class="small text-muted">
                    <i class="fas fa-building me-1"></i>
                    {{ profile.department.name }}
                </p>
                {% endif %}
                
                {% if profile.institution %}
                <p class="small text-muted">
                    <i class="fas fa-hospital me-1"></i>
                    {{ profile.institution.name }}
                </p>
                {% endif %}
                {% endif %}
                
                <div class="d-grid gap-2">
                    <a href="{% url 'accounts:profile_edit' %}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الملف الشخصي
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <h4 class="text-primary mb-1">0</h4>
                        <small class="text-muted">طلبات اليوم</small>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">0</h4>
                        <small class="text-muted">طلبات مكتملة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning mb-1">0</h4>
                        <small class="text-muted">في الانتظار</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-1">{{ user.date_joined|timesince }}</h4>
                        <small class="text-muted">منذ التسجيل</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Information -->
    <div class="col-lg-8">
        <!-- Personal Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم الأول</label>
                        <p class="fw-bold">{{ user.first_name|default:"غير محدد" }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الاسم الأخير</label>
                        <p class="fw-bold">{{ user.last_name|default:"غير محدد" }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">اسم المستخدم</label>
                        <p class="fw-bold">{{ user.username }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">البريد الإلكتروني</label>
                        <p class="fw-bold">{{ user.email|default:"غير محدد" }}</p>
                    </div>
                    {% if profile %}
                    {% if profile.phone %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الهاتف</label>
                        <p class="fw-bold">{{ profile.phone }}</p>
                    </div>
                    {% endif %}
                    {% if profile.employee_id %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">رقم الموظف</label>
                        <p class="fw-bold">{{ profile.employee_id }}</p>
                    </div>
                    {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Work Information -->
        {% if profile %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-briefcase me-2"></i>
                    معلومات العمل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الدور الوظيفي</label>
                        <p>
                            <span class="badge bg-primary">{{ profile.get_role_display }}</span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الحالة</label>
                        <p>
                            {% if profile.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </p>
                    </div>
                    {% if profile.institution %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">المؤسسة</label>
                        <p class="fw-bold">{{ profile.institution.name }}</p>
                    </div>
                    {% endif %}
                    {% if profile.department %}
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">القسم</label>
                        <p class="fw-bold">{{ profile.department.name }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- Account Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-cog me-2"></i>
                    معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ التسجيل</label>
                        <p class="fw-bold">{{ user.date_joined|date:"d/m/Y H:i" }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">آخر تسجيل دخول</label>
                        <p class="fw-bold">
                            {% if user.last_login %}
                            {{ user.last_login|date:"d/m/Y H:i" }}
                            {% else %}
                            لم يسجل دخول من قبل
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">حالة الحساب</label>
                        <p>
                            {% if user.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">معطل</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">صلاحيات المدير</label>
                        <p>
                            {% if user.is_staff %}
                            <span class="badge bg-warning text-dark">مدير</span>
                            {% else %}
                            <span class="badge bg-info">مستخدم عادي</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-2x text-muted mb-3"></i>
                    <h6 class="text-muted">لا يوجد نشاط حديث</h6>
                    <p class="text-muted small">سيتم عرض آخر الأنشطة هنا عند توفرها.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Example: Add tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
