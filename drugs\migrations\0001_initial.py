# Generated by Django 5.2.1 on 2025-05-26 15:26

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='DrugCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تصنيف الدواء',
                'verbose_name_plural': 'تصنيفات الأدوية',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Manufacturer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الشركة')),
                ('country', models.CharField(max_length=100, verbose_name='البلد')),
                ('contact_info', models.TextField(blank=True, verbose_name='معلومات الاتصال')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'الشركة المصنعة',
                'verbose_name_plural': 'الشركات المصنعة',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Drug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scientific_name', models.CharField(max_length=200, verbose_name='الاسم العلمي')),
                ('commercial_name', models.CharField(max_length=200, verbose_name='الاسم التجاري')),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='الباركود')),
                ('unit', models.CharField(choices=[('tablet', 'قرص'), ('capsule', 'كبسولة'), ('bottle', 'زجاجة'), ('vial', 'أمبولة'), ('tube', 'أنبوب'), ('box', 'علبة'), ('pack', 'عبوة'), ('ml', 'مل'), ('mg', 'مجم'), ('g', 'جرام')], max_length=20, verbose_name='الوحدة')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='سعر الوحدة')),
                ('strength', models.CharField(blank=True, max_length=100, verbose_name='التركيز')),
                ('dosage_form', models.CharField(blank=True, max_length=100, verbose_name='الشكل الصيدلاني')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('requires_prescription', models.BooleanField(default=True, verbose_name='يتطلب وصفة طبية')),
                ('is_controlled', models.BooleanField(default=False, verbose_name='دواء مراقب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='drugs.drugcategory', verbose_name='التصنيف')),
                ('manufacturer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='drugs.manufacturer', verbose_name='الشركة المصنعة')),
            ],
            options={
                'verbose_name': 'الدواء',
                'verbose_name_plural': 'الأدوية',
                'ordering': ['scientific_name'],
                'unique_together': {('scientific_name', 'manufacturer', 'strength')},
            },
        ),
    ]
