from django.shortcuts import render, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q
from .models import Drug, DrugCategory, Manufacturer
from .forms import DrugForm, DrugCategoryForm, ManufacturerForm

class DrugListView(LoginRequiredMixin, ListView):
    model = Drug
    template_name = 'drugs/drug_list.html'
    context_object_name = 'drugs'
    paginate_by = 20

    def get_queryset(self):
        queryset = Drug.objects.select_related('category', 'manufacturer').filter(is_active=True)

        # Search functionality
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(commercial_name__icontains=search) |
                Q(scientific_name__icontains=search) |
                Q(barcode__icontains=search)
            )

        # Category filter
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category_id=category)

        # Manufacturer filter
        manufacturer = self.request.GET.get('manufacturer')
        if manufacturer:
            queryset = queryset.filter(manufacturer_id=manufacturer)

        return queryset.order_by('commercial_name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = DrugCategory.objects.all()
        context['manufacturers'] = Manufacturer.objects.filter(is_active=True)
        context['search'] = self.request.GET.get('search', '')
        context['selected_category'] = self.request.GET.get('category', '')
        context['selected_manufacturer'] = self.request.GET.get('manufacturer', '')
        return context

class DrugDetailView(LoginRequiredMixin, DetailView):
    model = Drug
    template_name = 'drugs/drug_detail.html'
    context_object_name = 'drug'

class DrugCreateView(LoginRequiredMixin, CreateView):
    model = Drug
    form_class = DrugForm
    template_name = 'drugs/drug_form.html'
    success_url = reverse_lazy('drugs:drug_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إضافة الدواء بنجاح.')
        return super().form_valid(form)

class DrugUpdateView(LoginRequiredMixin, UpdateView):
    model = Drug
    form_class = DrugForm
    template_name = 'drugs/drug_form.html'
    success_url = reverse_lazy('drugs:drug_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الدواء بنجاح.')
        return super().form_valid(form)

class DrugDeleteView(LoginRequiredMixin, DeleteView):
    model = Drug
    template_name = 'drugs/drug_confirm_delete.html'
    success_url = reverse_lazy('drugs:drug_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الدواء بنجاح.')
        return super().delete(request, *args, **kwargs)

# Category Views
class CategoryListView(LoginRequiredMixin, ListView):
    model = DrugCategory
    template_name = 'drugs/category_list.html'
    context_object_name = 'categories'
    paginate_by = 20

class CategoryDetailView(LoginRequiredMixin, DetailView):
    model = DrugCategory
    template_name = 'drugs/category_detail.html'
    context_object_name = 'category'

class CategoryCreateView(LoginRequiredMixin, CreateView):
    model = DrugCategory
    form_class = DrugCategoryForm
    template_name = 'drugs/category_form.html'
    success_url = reverse_lazy('drugs:category_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إضافة التصنيف بنجاح.')
        return super().form_valid(form)

class CategoryUpdateView(LoginRequiredMixin, UpdateView):
    model = DrugCategory
    form_class = DrugCategoryForm
    template_name = 'drugs/category_form.html'
    success_url = reverse_lazy('drugs:category_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث التصنيف بنجاح.')
        return super().form_valid(form)

class CategoryDeleteView(LoginRequiredMixin, DeleteView):
    model = DrugCategory
    template_name = 'drugs/category_confirm_delete.html'
    success_url = reverse_lazy('drugs:category_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف التصنيف بنجاح.')
        return super().delete(request, *args, **kwargs)

# Manufacturer Views
class ManufacturerListView(LoginRequiredMixin, ListView):
    model = Manufacturer
    template_name = 'drugs/manufacturer_list.html'
    context_object_name = 'manufacturers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Manufacturer.objects.filter(is_active=True)

        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(country__icontains=search)
            )

        return queryset.order_by('name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        manufacturers = self.get_queryset()

        # Calculate statistics
        context['total_manufacturers'] = manufacturers.count()
        context['active_manufacturers'] = manufacturers.filter(is_active=True).count()
        context['total_countries'] = manufacturers.values('country').distinct().count()
        context['total_drugs'] = sum(m.drug_set.count() for m in manufacturers)

        return context

class ManufacturerDetailView(LoginRequiredMixin, DetailView):
    model = Manufacturer
    template_name = 'drugs/manufacturer_detail.html'
    context_object_name = 'manufacturer'

class ManufacturerCreateView(LoginRequiredMixin, CreateView):
    model = Manufacturer
    form_class = ManufacturerForm
    template_name = 'drugs/manufacturer_form.html'
    success_url = reverse_lazy('drugs:manufacturer_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم إضافة الشركة المصنعة بنجاح.')
        return super().form_valid(form)

class ManufacturerUpdateView(LoginRequiredMixin, UpdateView):
    model = Manufacturer
    form_class = ManufacturerForm
    template_name = 'drugs/manufacturer_form.html'
    success_url = reverse_lazy('drugs:manufacturer_list')

    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الشركة المصنعة بنجاح.')
        return super().form_valid(form)

class ManufacturerDeleteView(LoginRequiredMixin, DeleteView):
    model = Manufacturer
    template_name = 'drugs/manufacturer_confirm_delete.html'
    success_url = reverse_lazy('drugs:manufacturer_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف الشركة المصنعة بنجاح.')
        return super().delete(request, *args, **kwargs)

# API Views
def drug_search_api(request):
    """API للبحث عن الأدوية"""
    query = request.GET.get('q', '')
    if len(query) < 2:
        return JsonResponse({'results': []})

    drugs = Drug.objects.filter(
        Q(commercial_name__icontains=query) |
        Q(scientific_name__icontains=query) |
        Q(barcode__icontains=query),
        is_active=True
    )[:10]

    results = []
    for drug in drugs:
        results.append({
            'id': drug.id,
            'text': f"{drug.commercial_name} ({drug.scientific_name})",
            'commercial_name': drug.commercial_name,
            'scientific_name': drug.scientific_name,
            'unit': drug.get_unit_display(),
            'unit_price': str(drug.unit_price),
        })

    return JsonResponse({'results': results})

def drug_info_api(request, pk):
    """API للحصول على معلومات الدواء"""
    drug = get_object_or_404(Drug, pk=pk)

    data = {
        'id': drug.id,
        'commercial_name': drug.commercial_name,
        'scientific_name': drug.scientific_name,
        'category': drug.category.name,
        'manufacturer': drug.manufacturer.name,
        'unit': drug.get_unit_display(),
        'unit_price': str(drug.unit_price),
        'strength': drug.strength,
        'dosage_form': drug.dosage_form,
        'requires_prescription': drug.requires_prescription,
        'is_controlled': drug.is_controlled,
    }

    return JsonResponse(data)
