{% extends 'base/base.html' %}
{% load static %}

{% block title %}دفعات الأدوية - نظام إدارة الأدوية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'inventory:inventory_list' %}">إدارة المخزون</a></li>
        <li class="breadcrumb-item active">دفعات الأدوية</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-boxes me-2"></i>
                دفعات الأدوية
            </h1>
            <a href="{% url 'inventory:batch_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة دفعة جديدة
            </a>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h4 class="text-primary mb-1">{{ batches|length }}</h4>
                <small class="text-muted">إجمالي الدفعات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h4 class="text-success mb-1">
                    {% with active_count=0 %}
                        {% for batch in batches %}
                            {% if batch.is_active and not batch.is_expired %}
                                {% with active_count=active_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ active_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">دفعات صالحة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-warning">
            <div class="card-body">
                <h4 class="text-warning mb-1">
                    {% with expiring_count=0 %}
                        {% for batch in batches %}
                            {% if not batch.is_expired and batch.days_to_expiry <= 30 %}
                                {% with expiring_count=expiring_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ expiring_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">تنتهي قريباً</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h4 class="text-danger mb-1">
                    {% with expired_count=0 %}
                        {% for batch in batches %}
                            {% if batch.is_expired %}
                                {% with expired_count=expired_count|add:1 %}{% endwith %}
                            {% endif %}
                        {% endfor %}
                        {{ expired_count }}
                    {% endwith %}
                </h4>
                <small class="text-muted">منتهية الصلاحية</small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <a href="{% url 'inventory:expiring_soon' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-calendar-alt me-2"></i>
                            الدفعات التي تنتهي قريباً
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="{% url 'inventory:expired_drugs' %}" class="btn btn-outline-danger w-100">
                            <i class="fas fa-clock me-2"></i>
                            الدفعات المنتهية الصلاحية
                        </a>
                    </div>
                    <div class="col-md-4 mb-2">
                        <a href="#" class="btn btn-outline-info w-100">
                            <i class="fas fa-file-export me-2"></i>
                            تصدير تقرير الدفعات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Batches List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الدفعات ({{ batches|length }} دفعة)
                </h5>
            </div>
            <div class="card-body">
                {% if batches %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الدواء</th>
                                <th>رقم الدفعة</th>
                                <th>تاريخ التصنيع</th>
                                <th>تاريخ الانتهاء</th>
                                <th>المتبقي للانتهاء</th>
                                <th>سعر التكلفة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for batch in batches %}
                            <tr {% if batch.is_expired %}class="table-danger"{% elif batch.days_to_expiry <= 30 %}class="table-warning"{% endif %}>
                                <td>
                                    <div>
                                        <strong>{{ batch.drug.commercial_name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ batch.drug.scientific_name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <code>{{ batch.batch_number }}</code>
                                    {% if batch.supplier_batch_number %}
                                    <br><small class="text-muted">مورد: {{ batch.supplier_batch_number }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ batch.manufacturing_date|date:"d/m/Y" }}</td>
                                <td>
                                    <span class="{% if batch.is_expired %}text-danger{% elif batch.days_to_expiry <= 30 %}text-warning{% endif %}">
                                        {{ batch.expiry_date|date:"d/m/Y" }}
                                    </span>
                                </td>
                                <td>
                                    {% if batch.is_expired %}
                                    <span class="badge bg-danger">منتهي</span>
                                    {% elif batch.days_to_expiry <= 0 %}
                                    <span class="badge bg-danger">اليوم</span>
                                    {% elif batch.days_to_expiry <= 7 %}
                                    <span class="badge bg-danger">{{ batch.days_to_expiry }} يوم</span>
                                    {% elif batch.days_to_expiry <= 30 %}
                                    <span class="badge bg-warning text-dark">{{ batch.days_to_expiry }} يوم</span>
                                    {% else %}
                                    <span class="text-success">{{ batch.days_to_expiry }} يوم</span>
                                    {% endif %}
                                </td>
                                <td class="text-success fw-bold">{{ batch.cost_price }} ريال</td>
                                <td>
                                    {% if batch.is_recalled %}
                                    <span class="badge bg-dark">مسحوب</span>
                                    {% elif not batch.is_active %}
                                    <span class="badge bg-secondary">غير نشط</span>
                                    {% elif batch.is_expired %}
                                    <span class="badge bg-danger">منتهي</span>
                                    {% elif batch.days_to_expiry <= 30 %}
                                    <span class="badge bg-warning text-dark">ينتهي قريباً</span>
                                    {% else %}
                                    <span class="badge bg-success">صالح</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'inventory:batch_detail' batch.pk %}"
                                           class="btn btn-outline-info" title="عرض التفاصيل">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:batch_edit' batch.pk %}"
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if not batch.is_recalled %}
                                        <button type="button" class="btn btn-outline-danger"
                                                onclick="recallBatch({{ batch.pk }})" title="سحب الدفعة">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        {% endif %}
                                        <a href="{% url 'inventory:batch_delete' batch.pk %}"
                                           class="btn btn-outline-danger" title="حذف"
                                           onclick="return confirm('هل أنت متأكد من حذف هذه الدفعة؟')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="pagination">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                صفحة {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد دفعات</h5>
                    <p class="text-muted">لم يتم إضافة أي دفعات للأدوية بعد.</p>
                    <a href="{% url 'inventory:batch_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة أول دفعة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function recallBatch(batchId) {
    const reason = prompt('أدخل سبب سحب الدفعة:');
    if (reason) {
        // Here you would make an AJAX call to recall the batch
        fetch(`/inventory/batches/${batchId}/recall/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                'recall_reason': reason
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('حدث خطأ أثناء سحب الدفعة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء سحب الدفعة');
        });
    }
}

// Add CSRF token as meta tag
document.addEventListener('DOMContentLoaded', function() {
    if (!document.querySelector('meta[name="csrf-token"]')) {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            const meta = document.createElement('meta');
            meta.name = 'csrf-token';
            meta.content = csrfToken.value;
            document.head.appendChild(meta);
        }
    }
});
</script>
{% endblock %}
